<?php

/**
 * Demo test cho TestDuDoan10soVersion1Command
 */

/**
 * <PERSON>h tất cả tổ hợp 3 số từ mảng predictedNumbers
 */
function generateCombinationsFromPredicted($predictedNumbers)
{
    $combinations = [];
    $count = count($predictedNumbers);
    
    // Sinh tất cả tổ hợp 3 số theo vị trí
    for ($i = 0; $i < $count - 2; $i++) {
        for ($j = $i + 1; $j < $count - 1; $j++) {
            for ($k = $j + 1; $k < $count; $k++) {
                $combinations[] = [
                    $predictedNumbers[$i],
                    $predictedNumbers[$j], 
                    $predictedNumbers[$k]
                ];
            }
        }
    }
    
    return $combinations;
}

/**
 * Cập nhật dataBet với các tổ hợp mới từ predictedNumbers
 */
function updateDataBetWithPredicted($dataBet, $predictedNumbers)
{
    // Sinh tất cả tổ hợp 3 số
    $combinations = generateCombinationsFromPredicted($predictedNumbers);
    
    $updatedDataBet = [];
    
    // Cập nhật từng item trong dataBet với tổ hợp mới
    foreach ($combinations as $index => $combination) {
        // Nếu có item cũ thì giữ lại các thuộc tính khác, chỉ thay numberBet
        if (isset($dataBet[$index])) {
            $item = $dataBet[$index];
            $item['numberBet'] = $combination;
        } else {
            // Tạo item mới nếu không có
            $item = [
                'countMiss' => 0,
                'numberBet' => $combination,
                'amount' => 10,
                'status' => true,
            ];
        }
        
        $updatedDataBet[] = $item;
    }
    
    return $updatedDataBet;
}

/**
 * Demo logic version 1
 */
function demoVersion1()
{
    echo "=== DEMO TestDuDoan10soVersion1Command ===\n\n";
    
    $predictedCount = 10; // Dùng 10 số để demo (thực tế là 60)
    
    // Giả lập dữ liệu predicted numbers và results cho 8 kỳ
    $demoData = [
        // [predictedNumbers, result]
        [[15,1,16,8,23,67,35,42,55,77], [15, 1, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 16, 8, 23, 67, 42, 77]], // Trúng nhiều
        [[2,3,4,5,6,7,8,9,10,11], [2, 6, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61]],
        [[12,13,14,15,16,17,18,19,20,21], [3, 7, 11, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62]],
        [[22,23,24,25,26,27,28,29,30,31], [4, 8, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63]],
        [[32,33,34,35,36,37,38,39,40,41], [70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89]], // Trượt hoàn toàn
        [[42,43,44,45,46,47,48,49,50,51], [1, 5, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60]],
        [[52,53,54,55,56,57,58,59,60,61], [2, 6, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61]],
        [[62,63,64,65,66,67,68,69,70,71], [90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109]], // Trượt hoàn toàn
    ];
    
    $finalResult = array_fill(0, 45, 0);
    
    // Khởi tạo dataBet ban đầu
    $dataBet = [
        0 => [
            'countMiss' => 0,
            'numberBet' => [70,79,80],
            'amount' => 10,
            'status' => true,
        ],
        1 => [
            'countMiss' => 0,
            'numberBet' => [3,53,18],
            'amount' => 10,
            'status' => true,
        ],
    ];
    
    echo "Khởi tạo với " . count($dataBet) . " bộ số ban đầu:\n";
    foreach ($dataBet as $i => $item) {
        echo "  " . ($i + 1) . ". [" . implode(',', $item['numberBet']) . "]\n";
    }
    echo "\n";
    
    foreach ($demoData as $ky => $data) {
        $predictedNumbers = $data[0];
        $result = $data[1];
        
        echo "=== KỲ " . ($ky + 21) . " ===\n";
        echo "Predicted: [" . implode(',', $predictedNumbers) . "]\n";
        echo "Result: [" . implode(',', array_slice($result, 0, 10)) . "...]\n";
        
        // ===== GEN LẠI TOÀN BỘ numberBet TỪ PREDICTED NUMBERS =====
        if (!empty($predictedNumbers) && count($predictedNumbers) >= $predictedCount) {
            $predictedSlice = array_slice($predictedNumbers, 0, $predictedCount);
            $oldCount = count($dataBet);
            $dataBet = updateDataBetWithPredicted($dataBet, $predictedSlice);
            
            if ($ky == 0) {
                echo "Đã gen lại " . count($dataBet) . " tổ hợp từ $predictedCount số dự đoán (từ $oldCount bộ ban đầu)\n";
                echo "3 bộ đầu tiên sau khi gen:\n";
                for ($i = 0; $i < min(3, count($dataBet)); $i++) {
                    echo "  " . ($i + 1) . ". [" . implode(',', $dataBet[$i]['numberBet']) . "]\n";
                }
            }
        }
        // ===== KẾT THÚC GEN LẠI =====
        
        $hitCount = 0;
        $missCount = 0;
        $resetCount = 0;
        
        // Kiểm tra kết quả cho từng bộ số trong dataBet
        foreach ($dataBet as $key => $dataBetItem) {
            $numberBet = $dataBetItem['numberBet']; // Mảng 3 số [15, 1, 16]
            
            // Kiểm tra xem có ít nhất 1 số trùng với kết quả không
            $intersection = array_intersect($numberBet, $result);
            
            if (count($intersection) > 0) {
                // Trùng ít nhất 1 số => countMiss + 1
                $dataBetItem['countMiss'] += 1;
                $hitCount++;
            } else {
                // Không trùng số nào => thống kê fail streak và reset
                if ($dataBetItem['countMiss'] > 0) {
                    $failStreak = $dataBetItem['countMiss'];
                    if ($failStreak < 45) {
                        $finalResult[$failStreak] += 1;
                    }
                    
                    if ($resetCount < 3) { // Chỉ log 3 bộ đầu để không spam
                        echo "  Bộ [" . implode(',', $numberBet) . "] trượt hoàn toàn sau $failStreak lần miss\n";
                    }
                    $resetCount++;
                }
                $dataBetItem['countMiss'] = 0; // Reset
                $missCount++;
            }

            $dataBet[$key] = $dataBetItem; // Cập nhật lại
        }
        
        echo "Hit: $hitCount, Miss: $missCount, Reset: $resetCount\n";
        
        $totalMiss = array_sum(array_column($dataBet, 'countMiss'));
        $maxMiss = max(array_column($dataBet, 'countMiss'));
        $avgMiss = $totalMiss / count($dataBet);
        echo "Tổng " . count($dataBet) . " bộ số, Max miss = $maxMiss, Avg miss = " . round($avgMiss, 2) . "\n\n";
    }
    
    // Hiển thị kết quả cuối cùng
    echo "=== KẾT QUẢ THỐNG KÊ FAIL STREAK ===\n";
    echo "Fail Streak | Số lần xuất hiện\n";
    echo "------------|------------------\n";
    
    $totalEvents = 0;
    for ($i = 1; $i < 45; $i++) {
        if ($finalResult[$i] > 0) {
            echo sprintf("%11d | %16d\n", $i, $finalResult[$i]);
            $totalEvents += $finalResult[$i];
        }
    }
    
    echo "------------|------------------\n";
    echo sprintf("%11s | %16d\n", "TỔNG", $totalEvents);
    
    if ($totalEvents > 0) {
        echo "\n=== PHÂN TÍCH ===\n";
        echo "Tổng số lần trượt hoàn toàn: $totalEvents\n";
        
        $maxCount = max($finalResult);
        $mostCommonStreak = array_search($maxCount, $finalResult);
        echo "Fail streak phổ biến nhất: $mostCommonStreak lần ($maxCount lần xuất hiện)\n";
        
        $totalStreaks = 0;
        for ($i = 1; $i < 45; $i++) {
            $totalStreaks += $i * $finalResult[$i];
        }
        $avgStreak = $totalStreaks / $totalEvents;
        echo "Fail streak trung bình: " . round($avgStreak, 2) . " lần\n";
    }
    
    return $finalResult;
}

echo "=== DEMO LOGIC TestDuDoan10soVersion1Command ===\n\n";

echo "1. LOGIC CHÍNH:\n";
echo "- Mỗi kỳ gen lại TOÀN BỘ dataBet từ predicted numbers\n";
echo "- Kiểm tra: có ít nhất 1 số trùng => countMiss++\n";
echo "- Kiểm tra: không trùng số nào => thống kê failStreak và reset\n";
echo "- Thống kê tất cả vào finalResult chung\n\n";

echo "2. CÁCH GEN COMBO:\n";
echo "- Từ predictedNumbers = [15,1,16,8,23,67,35,42,55,77]\n";
echo "- dataBet[0]['numberBet'] = [15,1,16] (vị trí 0,1,2)\n";
echo "- dataBet[1]['numberBet'] = [15,1,8] (vị trí 0,1,3)\n";
echo "- dataBet[2]['numberBet'] = [15,1,23] (vị trí 0,1,4)\n";
echo "- ... tất cả tổ hợp 3 số\n\n";

echo "3. DEMO:\n\n";

$result = demoVersion1();

echo "\n4. CÁCH SỬ DỤNG THỰC TẾ:\n";
echo "php artisan app:du-doan-10-numberv1 --day=1 --port=5049\n\n";

echo "5. KẾT QUẢ MONG ĐỢI:\n";
echo "- Với 60 số sẽ có C(60,3) = 34,220 tổ hợp\n";
echo "- Thống kê fail streak cho TẤT CẢ các tổ hợp\n";
echo "- Kết quả dd(finalResult) để phân tích\n";

?>
