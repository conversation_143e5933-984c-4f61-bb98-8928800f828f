<?php

namespace App\Console\Commands;

use App\Services\KennoHistoryService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestDuDoan10soVersion1Command_Debug extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'app:du-doan-10-numberv1-debug {--day=} {--port=} ';
    const PORT = 5049;

    /**
     * Sinh tất cả tổ hợp 3 số từ mảng predictedNumbers
     */
    private function generateCombinationsFromPredicted($predictedNumbers)
    {
        $combinations = [];
        $count = count($predictedNumbers);

        echo "DEBUG: Sinh tổ hợp từ $count số: [" . implode(',', $predictedNumbers) . "]\n";

        // Sinh tất cả tổ hợp 3 số theo vị trí
        for ($i = 0; $i < $count - 2; $i++) {
            for ($j = $i + 1; $j < $count - 1; $j++) {
                for ($k = $j + 1; $k < $count; $k++) {
                    $combinations[] = [
                        $predictedNumbers[$i],
                        $predictedNumbers[$j],
                        $predictedNumbers[$k]
                    ];
                }
            }
        }

        echo "DEBUG: Đã sinh " . count($combinations) . " tổ hợp\n";
        echo "DEBUG: 5 tổ hợp đầu tiên:\n";
        for ($i = 0; $i < min(5, count($combinations)); $i++) {
            echo "  " . ($i + 1) . ". [" . implode(',', $combinations[$i]) . "]\n";
        }
        echo "\n";

        return $combinations;
    }

    /**
     * Cập nhật dataBet với các tổ hợp mới từ predictedNumbers
     */
    private function updateDataBetWithPredicted($dataBet, $predictedNumbers)
    {
        echo "DEBUG: updateDataBetWithPredicted - Input dataBet có " . count($dataBet) . " items\n";
        
        // Sinh tất cả tổ hợp 3 số
        $combinations = $this->generateCombinationsFromPredicted($predictedNumbers);

        $updatedDataBet = [];

        // Cập nhật từng item trong dataBet với tổ hợp mới
        foreach ($combinations as $index => $combination) {
            // Nếu có item cũ thì giữ lại các thuộc tính khác, chỉ thay numberBet
            if (isset($dataBet[$index])) {
                $item = $dataBet[$index];
                $oldCountMiss = $item['countMiss'];
                $item['numberBet'] = $combination;
                
                if ($index < 3) { // Log 3 item đầu
                    echo "DEBUG: Item $index - Giữ countMiss=$oldCountMiss, thay numberBet=[" . implode(',', $combination) . "]\n";
                }
            } else {
                // Tạo item mới nếu không có
                $item = [
                    'countMiss' => 0,
                    'numberBet' => $combination,
                    'amount' => 10,
                    'status' => true,
                ];
                
                if ($index < 3) { // Log 3 item đầu
                    echo "DEBUG: Item $index - Tạo mới countMiss=0, numberBet=[" . implode(',', $combination) . "]\n";
                }
            }

            $updatedDataBet[] = $item;
        }

        echo "DEBUG: updateDataBetWithPredicted - Output dataBet có " . count($updatedDataBet) . " items\n\n";
        return $updatedDataBet;
    }

    public function handle(): int
    {
        $dayTest = $this->option('day') ?? 1;
        $port = $this->option('port') ?? self::PORT;
        $predictedCount = 9; // DEBUG: Sử dụng 9 số để kiểm tra

        $service = app()->make(KennoHistoryService::class);
        $data = $service->getResultByDay($dayTest);

        // Parse history
        $history = collect($data)->map(fn($r) => array_map('intval', explode('|', $r['resultRaw'])))->toArray();

        $a = 20; // kỳ bắt đầu
        $b = 25; // DEBUG: Chỉ chạy 5 kỳ để debug chi tiết
        $finalResult = array_fill(0, 45, 0);

        // Khởi tạo dataBet ban đầu
        $dataBet = [
            0 => [
                'countMiss'   => 0,
                'numberBet'   => [70,79,80],
                'amount'      => 10,
                'status'      => true,
            ],
            1 => [
                'countMiss'   => 0,
                'numberBet'   => [3,53,18],
                'amount'      => 10,
                'status'      => true,
            ],
        ];

        echo "=== DEBUG: BẮT ĐẦU THỐNG KÊ FAIL STREAK ===\n";
        echo "Ngày test: $dayTest, Port: $port\n";
        echo "Số lượng số dự đoán: $predictedCount\n";
        echo "Kỳ từ $a đến $b (DEBUG: chỉ 5 kỳ)\n";
        echo "Khởi tạo dataBet với " . count($dataBet) . " items\n\n";

        // Tính số tổ hợp mong đợi
        $expectedCombinations = 0;
        for ($i = 0; $i < $predictedCount - 2; $i++) {
            for ($j = $i + 1; $j < $predictedCount - 1; $j++) {
                for ($k = $j + 1; $k < $predictedCount; $k++) {
                    $expectedCombinations++;
                }
            }
        }
        echo "DEBUG: Với $predictedCount số, mong đợi $expectedCombinations tổ hợp\n";
        echo "DEBUG: Công thức C($predictedCount,3) = " . ($predictedCount * ($predictedCount-1) * ($predictedCount-2)) / 6 . "\n\n";

        for ($ky = $a; $ky <= $b; $ky++) {
            echo "=== DEBUG KỲ $ky ===\n";
            
            $result = $history[$ky] ?? null;
            if (!$result) {
                echo "DEBUG: Không có kết quả cho kỳ $ky\n\n";
                continue;
            }

            echo "DEBUG: Kết quả kỳ $ky: [" . implode(',', array_slice($result, 0, 10)) . "...] (20 số)\n";

            $rowPredict = array_slice($history, 0, $ky);

            // Giả lập predicted numbers để debug (thay vì gọi API)
            $mockPredicted = [
                20 => [1,2,3,4,5,6,7,8,9],
                21 => [10,11,12,13,14,15,16,17,18],
                22 => [19,20,21,22,23,24,25,26,27],
                23 => [28,29,30,31,32,33,34,35,36],
                24 => [37,38,39,40,41,42,43,44,45],
                25 => [46,47,48,49,50,51,52,53,54],
            ];
            
            $predictedNumbers = $mockPredicted[$ky] ?? [1,2,3,4,5,6,7,8,9];
            echo "DEBUG: Predicted numbers: [" . implode(',', $predictedNumbers) . "]\n";

            // ===== GEN LẠI TOÀN BỘ numberBet TỪ PREDICTED NUMBERS =====
            if (!empty($predictedNumbers) && count($predictedNumbers) >= $predictedCount) {
                $predictedSlice = array_slice($predictedNumbers, 0, $predictedCount);
                
                echo "DEBUG: Trước khi gen - dataBet có " . count($dataBet) . " items\n";
                $dataBet = $this->updateDataBetWithPredicted($dataBet, $predictedSlice);
                echo "DEBUG: Sau khi gen - dataBet có " . count($dataBet) . " items\n";
            } else {
                echo "DEBUG: Không đủ predicted numbers, bỏ qua\n\n";
                continue;
            }

            // Kiểm tra kết quả cho từng bộ số trong dataBet
            $hitCount = 0;
            $missCount = 0;
            $resetCount = 0;
            
            echo "DEBUG: Bắt đầu kiểm tra " . count($dataBet) . " bộ số\n";
            
            foreach ($dataBet as $key => $dataBetItem) {
                $numberBet = $dataBetItem['numberBet']; // Mảng 3 số [15, 1, 16]
                $oldCountMiss = $dataBetItem['countMiss'];

                // Kiểm tra xem có ít nhất 1 số trùng với kết quả không
                $intersection = array_intersect($numberBet, $result);

                if (count($intersection) > 0) {
                    // Trùng ít nhất 1 số => countMiss + 1
                    $dataBetItem['countMiss'] += 1;
                    $hitCount++;
                    
                    if ($key < 3) { // Log 3 item đầu
                        echo "DEBUG: Item $key [" . implode(',', $numberBet) . "] HIT (trùng " . count($intersection) . " số), countMiss: $oldCountMiss -> " . $dataBetItem['countMiss'] . "\n";
                    }
                } else {
                    // Không trùng số nào => thống kê fail streak và reset
                    if ($dataBetItem['countMiss'] > 0) {
                        $failStreak = $dataBetItem['countMiss'];
                        if ($failStreak < 45) {
                            $finalResult[$failStreak] += 1;
                        }
                        
                        if ($key < 3) { // Log 3 item đầu
                            echo "DEBUG: Item $key [" . implode(',', $numberBet) . "] MISS HOÀN TOÀN sau $failStreak lần, thống kê vào finalResult[$failStreak]\n";
                        }
                        $resetCount++;
                    } else {
                        if ($key < 3) { // Log 3 item đầu
                            echo "DEBUG: Item $key [" . implode(',', $numberBet) . "] MISS nhưng countMiss=0, không thống kê\n";
                        }
                    }
                    $dataBetItem['countMiss'] = 0; // Reset
                    $missCount++;
                }

                $dataBet[$key] = $dataBetItem; // Cập nhật lại
            }

            echo "DEBUG: Kết quả kỳ $ky - Hit: $hitCount, Miss: $missCount, Reset (thống kê): $resetCount\n";
            
            $totalMiss = array_sum(array_column($dataBet, 'countMiss'));
            $maxMiss = max(array_column($dataBet, 'countMiss'));
            $avgMiss = $totalMiss / count($dataBet);
            echo "DEBUG: Tổng " . count($dataBet) . " bộ số, Max miss = $maxMiss, Avg miss = " . round($avgMiss, 2) . "\n";
            
            echo "DEBUG: finalResult hiện tại: ";
            $nonZero = [];
            for ($i = 1; $i < 10; $i++) {
                if ($finalResult[$i] > 0) {
                    $nonZero[] = "$i:$finalResult[$i]";
                }
            }
            echo "[" . implode(', ', $nonZero) . "]\n\n";
        }

        // Hiển thị kết quả cuối cùng
        echo "=== DEBUG: KẾT QUẢ THỐNG KÊ FAIL STREAK ===\n";
        echo "Fail Streak | Số lần xuất hiện\n";
        echo "------------|------------------\n";

        $totalEvents = 0;
        for ($i = 1; $i < 45; $i++) {
            if ($finalResult[$i] > 0) {
                echo sprintf("%11d | %16d\n", $i, $finalResult[$i]);
                $totalEvents += $finalResult[$i];
            }
        }

        echo "------------|------------------\n";
        echo sprintf("%11s | %16d\n", "TỔNG", $totalEvents);

        echo "\n=== DEBUG: XÁC MINH TÍNH CHÍNH XÁC ===\n";
        echo "1. Số tổ hợp mong đợi với 9 số: C(9,3) = " . (9*8*7)/6 . " = 84 tổ hợp\n";
        echo "2. Số tổ hợp thực tế: " . count($dataBet) . " tổ hợp\n";
        echo "3. Tổng số lần thống kê: $totalEvents\n";
        echo "4. Logic kiểm tra:\n";
        echo "   - Có intersection > 0: countMiss++\n";
        echo "   - Không có intersection: thống kê countMiss và reset = 0\n";
        echo "5. Kết quả có hợp lý không?\n";

        return 0;
    }
}
