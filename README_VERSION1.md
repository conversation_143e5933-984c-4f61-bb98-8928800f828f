# TestDuDoan10soVersion1Command - THỐNG KÊ FAIL STREAK TỪ PREDICTED NUMBERS

## <PERSON><PERSON><PERSON> đ<PERSON>ch

Class `TestDuDoan10soVersion1Command` được tạo để thống kê fail streak của các bộ 3 số đượ<PERSON> sinh từ predicted numbers của AI, theo đúng yêu cầu của bạn.

## Logic hoạt động

### 1. **Khởi tạo**
```php
$history = collect($data)->map(fn($r) => array_map('intval', explode('|', $r['resultRaw'])))->toArray();
// Format: [[1,15,8,23,...,77], [2,17,8,23,...,66]]

$finalResult = array_fill(0, 45, 0); // Thống kê fail streak
$predictedCount = 60; // Số lượng số dự đoán (có thể điều chỉnh 60 hoặc 70)
```

### 2. **Mỗi kỳ xử lý**
```php
for ($ky = $a; $ky <= $b; $ky++) {
    // 1. L<PERSON>y kết quả kỳ hiện tại
    $result = $history[$ky];
    
    // 2. Dự đoán từ AI
    $rowPredict = array_slice($history, 0, $ky);
    $predictedNumbers = callAI($rowPredict); // [17,4,6,22,59,...,26,78,35]
    
    // 3. Gen lại TOÀN BỘ dataBet từ predicted numbers
    $dataBet = $this->updateDataBetWithPredicted($dataBet, $predictedNumbers);
    
    // 4. Kiểm tra từng bộ số
    foreach ($dataBet as $key => $dataBetItem) {
        $numberBet = $dataBetItem['numberBet']; // [15, 1, 16]
        $intersection = array_intersect($numberBet, $result);
        
        if (count($intersection) > 0) {
            // Có ít nhất 1 số trùng => countMiss++
            $dataBetItem['countMiss'] += 1;
        } else {
            // Không trùng số nào => thống kê và reset
            $finalResult[$dataBetItem['countMiss']] += 1;
            $dataBetItem['countMiss'] = 0;
        }
    }
}
```

### 3. **Cách gen combo từ predicted numbers**

Với `$predictedNumbers = [15,1,16,8,23,67,35,42,55,77]` (60 số):

```php
$dataBet[0]['numberBet'] = [$predictedNumbers[0], $predictedNumbers[1], $predictedNumbers[2]]  // [15,1,16]
$dataBet[1]['numberBet'] = [$predictedNumbers[0], $predictedNumbers[1], $predictedNumbers[3]]  // [15,1,8]
$dataBet[2]['numberBet'] = [$predictedNumbers[0], $predictedNumbers[1], $predictedNumbers[4]]  // [15,1,23]
...
// Tất cả tổ hợp C(60,3) = 34,220 bộ
```

## Điểm khác biệt với các version khác

| Aspect | Version 1 | checkCall4 |
|--------|-----------|------------|
| **Gen combo** | Trực tiếp từ predicted numbers | Qua updateDataBetWithCombinations |
| **Format numberBet** | [15,1,16] (integer) | ["15","01","16"] (string 2 chữ số) |
| **Logic kiểm tra** | array_intersect trực tiếp | array_diff như truotXien4 |
| **Mục đích** | Đơn giản, trực tiếp | Tương thích với truotXien4 |

## Kết quả đầu ra

### 1. **Trong quá trình chạy**
```
=== BẮT ĐẦU THỐNG KÊ FAIL STREAK ===
Ngày test: 1, Port: 5049
Số lượng số dự đoán: 60
Kỳ từ 20 đến 119

Kỳ 20: Đã gen lại 34220 tổ hợp từ 60 số dự đoán
Ví dụ 3 bộ đầu tiên:
  1. [15,1,16]
  2. [15,1,8]
  3. [15,1,23]

Kỳ 40: Bộ [25,30,35] trượt hoàn toàn sau 5 lần miss
Kỳ 40: Tổng 34220 bộ số, Max miss = 12, Avg miss = 3.45
```

### 2. **Kết quả cuối cùng**
```
=== KẾT QUẢ THỐNG KÊ FAIL STREAK ===
Fail Streak | Số lần xuất hiện
------------|------------------
          1 |             2341
          2 |            15740
          3 |             8521
          4 |             5234
          5 |             3012
------------|------------------
     TỔNG |            34848

=== PHÂN TÍCH CHI TIẾT ===
Tổng số lần trượt hoàn toàn: 34,848
Fail streak phổ biến nhất: 2 lần (15,740 lần xuất hiện)
Fail streak trung bình: 2.85 lần
```

### 3. **dd($finalResult)**
```php
array:45 [
  0 => 0
  1 => 2341
  2 => 15740
  3 => 8521
  4 => 5234
  5 => 3012
  ...
  44 => 0
]
```

## Cách sử dụng

### Command line
```bash
# Chạy thống kê với 60 số dự đoán
php artisan app:du-doan-10-numberv1 --day=1 --port=5049

# Chạy với ngày khác
php artisan app:du-doan-10-numberv1 --day=2 --port=5048
```

### Điều chỉnh tham số
```php
$predictedCount = 70; // Thay đổi từ 60 lên 70 số
```

## Ứng dụng

### 1. **Phân tích pattern**
- Xem bộ 3 số dự đoán từ AI thường trượt sau bao nhiêu lần
- Tìm ngưỡng fail streak phổ biến nhất
- Đánh giá độ chính xác của AI predict

### 2. **Tối ưu strategy**
- Xác định ngưỡng bet tối ưu dựa trên fail streak
- Tính toán risk/reward cho từng ngưỡng
- So sánh hiệu quả giữa 60 và 70 số dự đoán

### 3. **Validation AI**
- Kiểm tra chất lượng predicted numbers
- So sánh performance giữa các model AI
- Tìm số lượng predicted numbers tối ưu

## Files liên quan

1. `TestDuDoan10soVersion1Command.php` - Command chính
2. `test_version1_demo.php` - Demo với dữ liệu giả lập
3. `README_VERSION1.md` - Tài liệu này

## Lưu ý

- **Memory**: Với 60 số có ~34K tổ hợp, cần đủ RAM
- **Performance**: Xử lý 100 kỳ có thể mất vài phút
- **Network**: Cần server Python chạy để predict
- **Flexibility**: Có thể điều chỉnh predictedCount từ 60-70

## Kết luận

`TestDuDoan10soVersion1Command` là công cụ đơn giản và trực tiếp để:
- ✅ Thống kê fail streak từ AI predicted numbers
- ✅ Gen combo trực tiếp từ predicted numbers
- ✅ Logic đơn giản, dễ hiểu
- ✅ Kết quả dd($finalResult) để phân tích chi tiết
- ✅ Linh hoạt điều chỉnh số lượng predicted numbers
