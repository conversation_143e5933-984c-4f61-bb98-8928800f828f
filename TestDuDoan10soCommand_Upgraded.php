<?php

namespace App\Console\Commands;

use App\Services\KennoHistoryService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use PhpParser\Node\Stmt\Echo_;

class TestDuDoan10soCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'app:du-doan-10-number {--day=} {--port=} {--isSort=} {--mode=}';
    // php artisan app:du-doan-10-number --port=5049 --day=1 --mode=checkCall4
    const PORT = 5048;

    /**
     * The console command description.
     */
    protected $description = "yu";

    // -----------------------------

    /**
     * Sinh tất cả tổ hợp từ mảng predictedNumbers
     * @param array $predictedNumbers - Mảng 60-70 số dự đoán
     * @param int $comboSize - <PERSON>ích thước tổ hợp (3 hoặc 4)
     * @return array - Mảng các tổ hợp index
     */
    private function generateCombinationsFromPredicted($predictedNumbers, $comboSize)
    {
        $combinations = [];
        $count = count($predictedNumbers);
        
        if ($comboSize == 3) {
            // Sinh tất cả tổ hợp 3 số theo vị trí
            for ($i = 0; $i < $count - 2; $i++) {
                for ($j = $i + 1; $j < $count - 1; $j++) {
                    for ($k = $j + 1; $k < $count; $k++) {
                        $combinations[] = [$i, $j, $k]; // Trả về index để tương thích với code cũ
                    }
                }
            }
        } elseif ($comboSize == 4) {
            // Sinh tất cả tổ hợp 4 số theo vị trí
            for ($i = 0; $i < $count - 3; $i++) {
                for ($j = $i + 1; $j < $count - 2; $j++) {
                    for ($k = $j + 1; $k < $count - 1; $k++) {
                        for ($l = $k + 1; $l < $count; $l++) {
                            $combinations[] = [$i, $j, $k, $l]; // Trả về index để tương thích với code cũ
                        }
                    }
                }
            }
        }
        
        return $combinations;
    }

    public function handle(): int
    {
        $dayTest = $this->option('day') ?? 1;
        $port = $this->option('port') ?? self::PORT;
        $isSort = $this->option('isSort') ?? false;
        $mode = $this->option('mode') ?? 'checkCall';

        switch ($mode) {
            case 'checkCall4':
                echo "=== CHẠY CHẾ ĐỘ THỐNG KÊ FAIL STREAK ===\n";
                $result = $this->checkCall4($dayTest, $port);
                return 0;

            case 'checkCall':
            default:
                echo "=== CHẠY CHẾ ĐỘ THỐNG KÊ THƯỜNG ===\n";
                $arrayFill = [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];
//                if ($dayTest == 0) {
//                    for ($i = 1; $i <= 2; $i++) {
//                        $x = $this->checkCall($i, $port, $isSort);
//                        $arrayFill[$x]++;
//                    }
//                } else {
                    dd($this->checkCall($dayTest, $port));
//                }
//                dd($arrayFill);
//                return $this->checkCall($dayTest, $port);
        }
    }

    public function checkCall(int $dayTest, int $port, bool $isSort = false)
    {

        $comboSize   = 3; // 👈 CHỈNH 3 hoặc 4 ở đây
        $sliceCount  = 70; // 👈 NÂNG CẤP: Có thể điều chỉnh từ 60 lên 70
        $topGet      = 5;
        $topNGap     = 3;
        $tripletSize = 2;

        $service = app()->make(KennoHistoryService::class);
        $data    = $service->getResultByDay($dayTest);
        $history = collect($data)->map(fn($r) => array_map('intval', explode('|', $r['resultRaw'])))->toArray();

        $a = 20; $b = 119;
        $gapStats = [];
        $lastTruotIndex = null;

        // ===== NÂNG CẤP: KHỞI TẠO BIẾN COMBO =====
        // Khởi tạo với cách cũ (sẽ được thay thế khi có predictedNumbers)
        $comboIndices = $this->combinations(range(0, $sliceCount - 1), $comboSize);
        $comboCount = count($comboIndices);
        $comboLastMiss = array_fill(0, $comboCount, null);
        $comboGapStats = [];
        // ===== KẾT THÚC KHỞI TẠO =====

        $log = [];

        for ($ky = $a; $ky <= $b; $ky++) {
            $result = $history[$ky] ?? null;
            if (!$result) continue;

            $rounds70 = array_slice($history, 0, $ky);
            $response = Http::timeout(60)->post("http://127.0.0.1:{$port}/predict-10-number", [
                'draws_70' => $rounds70,
            ]);
            if (!$response->ok()) {
                echo "Failed at kỳ $ky: " . $response->body() . "\n";
                continue;
            }

            $predicted = json_decode($response->body(), true);
            $predictedNumbers = $predicted['top10_miss_numbers'] ?? [];

            // ===== NÂNG CẤP: GEN LẠI COMBO TỪ PREDICTED NUMBERS =====
            // Chỉ gen lại khi có predictedNumbers với số lượng đủ
            if (!empty($predictedNumbers) && count($predictedNumbers) >= $sliceCount) {
                // Lấy đúng số lượng cần thiết
                $predictedSlice = array_slice($predictedNumbers, 0, $sliceCount);
                
                // Gen lại combo từ predicted numbers
                $newComboIndices = $this->generateCombinationsFromPredicted($predictedSlice, $comboSize);
                
                // Cập nhật nếu số lượng combo thay đổi
                if (count($newComboIndices) != $comboCount) {
                    $comboIndices = $newComboIndices;
                    $comboCount = count($comboIndices);
                    $comboLastMiss = array_fill(0, $comboCount, null);
                    Log::info("Kỳ $ky: Đã gen lại " . $comboCount . " tổ hợp từ " . $sliceCount . " số dự đoán");
                } else {
                    $comboIndices = $newComboIndices;
                }
            }
            // ===== KẾT THÚC NÂNG CẤP =====

            // Gốc: thống kê topN
            $hits = array_intersect($predictedNumbers, $result);
            $top5 = array_slice($predictedNumbers, 0, $topGet);
            $bottom5 = array_slice($predictedNumbers, -5);

            $topN = array_slice($predictedNumbers, 0, $topNGap);
            if (count(array_intersect($topN, $result)) === 0) {
                if ($lastTruotIndex !== null) {
                    $gapStats[$ky - $lastTruotIndex] = ($gapStats[$ky - $lastTruotIndex] ?? 0) + 1;
                }
                $lastTruotIndex = $ky;
            }

            // Mở rộng: xét toàn bộ combo từ predicted numbers
            $last10 = array_slice($predictedNumbers, 0, $sliceCount);
            $result2 = array_map(fn($n) => str_pad((string)$n, $tripletSize, '0', STR_PAD_LEFT), $result);

            foreach ($comboIndices as $idx => $combo) {
                $trip = array_map(fn($i) => str_pad((string)$last10[$i], $tripletSize, '0', STR_PAD_LEFT), $combo);
                if (count(array_intersect($trip, $result2)) === 0) {
                    if ($comboLastMiss[$idx] !== null) {
                        $gap = $ky - $comboLastMiss[$idx];
                        $comboGapStats[$gap] = ($comboGapStats[$gap] ?? 0) + 1;
                    }
                    $comboLastMiss[$idx] = $ky;
                }
            }

            $log[] = [
                'ky'           => $ky,
                'hit_count'    => count($hits),
                'hit_top5'     => count(array_intersect($top5, $result)),
                'hit_bottom5'  => count(array_intersect($bottom5, $result)),
                'trung'        => array_values($hits),
                'top5'         => $top5,
                'bottom5'      => $bottom5,
                'duDoan'       => $predictedNumbers,
                'result'       => $result,
                'combo_count'  => $comboCount, // Thêm thông tin số lượng combo
            ];
        }

        echo "\n=== Thống kê khoảng cách trượt hoàn toàn top{$topNGap} ===\n";
        ksort($gapStats);
        foreach ($gapStats as $g => $c) {
            echo "- Cách nhau {$g} kỳ: {$c} lần\n";
        }

        echo "\n=== Thống kê khoảng cách trượt từ tất cả C({$sliceCount},{$comboSize}) ===\n";
        ksort($comboGapStats);
        foreach ($comboGapStats as $g => $c) {
            echo "- Cách nhau {$g} kỳ: {$c} lần\n";
        }

        return $log ? $log[count($log) - 1]['trung'] : [];
    }

    // Hàm combinations gắn trong class (giữ nguyên để tương thích)
    private function combinations(array $array, int $r): array {
        $results = [];
        $n = count($array);
        if ($r > $n) return [];

        $indexes = range(0, $r - 1);
        while (true) {
            $results[] = array_map(fn($i) => $array[$i], $indexes);

            for ($i = $r - 1; $i >= 0; $i--) {
                if ($indexes[$i] !== $i + $n - $r) break;
            }

            if ($i < 0) break;

            $indexes[$i]++;
            for ($j = $i + 1; $j < $r; $j++) {
                $indexes[$j] = $indexes[$j - 1] + 1;
            }
        }
        return $results;
    }

    /**
     * Hàm thống kê fail streak của các bộ 3 số - tương tự truotXien4 nhưng để thống kê
     * @param int $dayTest
     * @param int $port
     * @return array
     */
    public function checkCall4(int $dayTest, int $port)
    {
        $comboSize = 3; // Bộ 3 số
        $sliceCount = 60; // Lấy 60 số từ dự đoán

        // Lấy dữ liệu 1 ngày
        $service = app()->make(KennoHistoryService::class);
        $data = $service->getResultByDay($dayTest);
        $history = collect($data)->map(fn($r) => array_map('intval', explode('|', $r['resultRaw'])))->toArray();

        $a = 20;
        $b = 119;

        // Khởi tạo biến thống kê
        $dataResult = array_fill(0, 45, 0); // Thống kê fail streak từ 0 đến 44

        // Khởi tạo combo và countMiss
        $comboIndices = [];
        $comboCountMiss = [];
        $comboCount = 0;

        echo "=== BẮT ĐẦU THỐNG KÊ FAIL STREAK CÁC BỘ 3 SỐ ===\n";
        echo "Ngày test: $dayTest, Port: $port\n";
        echo "Kỳ từ $a đến $b\n\n";

        for ($ky = $a; $ky <= $b; $ky++) {
            $result = $history[$ky] ?? null;
            if (!$result) continue;

            $rounds70 = array_slice($history, 0, $ky);
            $response = Http::timeout(60)->post("http://127.0.0.1:{$port}/predict-10-number", [
                'draws_70' => $rounds70,
            ]);

            if (!$response->ok()) {
                echo "Failed at kỳ $ky: " . $response->body() . "\n";
                continue;
            }

            $predicted = json_decode($response->body(), true);
            $predictedNumbers = $predicted['top10_miss_numbers'] ?? [];

            // ===== GEN LẠI COMBO TỪ PREDICTED NUMBERS =====
            if (!empty($predictedNumbers) && count($predictedNumbers) >= $sliceCount) {
                $predictedSlice = array_slice($predictedNumbers, 0, $sliceCount);
                $newComboIndices = $this->generateCombinationsFromPredicted($predictedSlice, $comboSize);

                // Nếu là lần đầu hoặc số lượng combo thay đổi
                if (empty($comboIndices) || count($newComboIndices) != $comboCount) {
                    $comboIndices = $newComboIndices;
                    $comboCount = count($comboIndices);
                    $comboCountMiss = array_fill(0, $comboCount, 0);
                    echo "Kỳ $ky: Khởi tạo " . $comboCount . " tổ hợp từ " . $sliceCount . " số dự đoán\n";
                } else {
                    $comboIndices = $newComboIndices;
                }
            } else {
                echo "Kỳ $ky: Không đủ predicted numbers, bỏ qua\n";
                continue;
            }

            // Convert result sang format string 2 chữ số để so sánh
            $resultFormatted = array_map(fn($n) => str_pad((string)$n, 2, '0', STR_PAD_LEFT), $result);

            // Kiểm tra từng bộ 3 số
            foreach ($comboIndices as $idx => $combo) {
                // Lấy 3 số từ predicted numbers theo index combo
                $triplet = array_map(fn($i) => str_pad((string)$predictedSlice[$i], 2, '0', STR_PAD_LEFT), $combo);

                // Kiểm tra xem có ít nhất 1 số trùng với kết quả không
                $intersection = array_intersect($triplet, $resultFormatted);

                if (count($intersection) > 0) {
                    // Có ít nhất 1 số trùng => countMiss + 1
                    $comboCountMiss[$idx]++;
                } else {
                    // Cả 3 số đều trượt => thống kê fail streak và reset
                    if ($comboCountMiss[$idx] > 0) {
                        $failStreak = $comboCountMiss[$idx];
                        if ($failStreak < 45) {
                            $dataResult[$failStreak]++;
                        }
                        echo "Kỳ $ky: Bộ [" . implode(',', $triplet) . "] trượt hoàn toàn sau $failStreak lần miss\n";
                    }
                    $comboCountMiss[$idx] = 0;
                }
            }

            // Log tiến trình
            if ($ky % 10 == 0) {
                $maxMiss = max($comboCountMiss);
                $avgMiss = array_sum($comboCountMiss) / count($comboCountMiss);
                echo "Kỳ $ky: Max miss = $maxMiss, Avg miss = " . round($avgMiss, 2) . "\n";
            }
        }

        // Thống kê cuối cùng cho các bộ chưa trượt hoàn toàn
        foreach ($comboCountMiss as $idx => $missCount) {
            if ($missCount > 0) {
                $triplet = array_map(fn($i) => str_pad((string)$predictedSlice[$i], 2, '0', STR_PAD_LEFT), $comboIndices[$idx]);
                echo "Bộ [" . implode(',', $triplet) . "] kết thúc với $missCount lần miss (chưa trượt hoàn toàn)\n";
            }
        }

        // Hiển thị kết quả thống kê
        echo "\n=== KẾT QUẢ THỐNG KÊ FAIL STREAK ===\n";
        echo "Fail Streak | Số lần xuất hiện\n";
        echo "------------|------------------\n";

        $totalEvents = 0;
        for ($i = 1; $i < 45; $i++) {
            if ($dataResult[$i] > 0) {
                echo sprintf("%11d | %16d\n", $i, $dataResult[$i]);
                $totalEvents += $dataResult[$i];
            }
        }

        echo "------------|------------------\n";
        echo sprintf("%11s | %16d\n", "TỔNG", $totalEvents);

        // Thống kê chi tiết
        echo "\n=== PHÂN TÍCH CHI TIẾT ===\n";
        if ($totalEvents > 0) {
            echo "Tổng số lần trượt hoàn toàn: $totalEvents\n";

            // Tìm fail streak phổ biến nhất
            $maxCount = max($dataResult);
            $mostCommonStreak = array_search($maxCount, $dataResult);
            echo "Fail streak phổ biến nhất: $mostCommonStreak lần ($maxCount lần xuất hiện)\n";

            // Tính fail streak trung bình
            $totalStreaks = 0;
            for ($i = 1; $i < 45; $i++) {
                $totalStreaks += $i * $dataResult[$i];
            }
            $avgStreak = $totalStreaks / $totalEvents;
            echo "Fail streak trung bình: " . round($avgStreak, 2) . " lần\n";

            // Thống kê theo khoảng
            $ranges = [
                '1-5' => [1, 5],
                '6-10' => [6, 10],
                '11-15' => [11, 15],
                '16-20' => [16, 20],
                '21-25' => [21, 25],
                '26-30' => [26, 30],
                '31+' => [31, 44]
            ];

            echo "\nThống kê theo khoảng:\n";
            foreach ($ranges as $label => $range) {
                $count = 0;
                for ($i = $range[0]; $i <= $range[1]; $i++) {
                    $count += $dataResult[$i];
                }
                $percent = round($count / $totalEvents * 100, 1);
                echo "$label lần: $count ($percent%)\n";
            }
        }

        return $dataResult;
    }

}
