<?php

namespace App\Console\Commands;

use App\Services\KennoHistoryService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestDuDoan10soVersion1Command extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'app:du-doan-10-numberv1 {--day=} {--port=} ';
    const PORT = 5049;

    /**
     * Sinh tất cả tổ hợp 3 số từ mảng predictedNumbers
     * @param array $predictedNumbers - Mảng số dự đoán
     * @return array - Mảng các tổ hợp 3 số
     */
    private function generateCombinationsFromPredicted($predictedNumbers)
    {
        $combinations = [];
        $count = count($predictedNumbers);
        
        // Sinh tất cả tổ hợp 3 số theo vị trí
        for ($i = 0; $i < $count - 2; $i++) {
            for ($j = $i + 1; $j < $count - 1; $j++) {
                for ($k = $j + 1; $k < $count; $k++) {
                    $combinations[] = [
                        $predictedNumbers[$i],
                        $predictedNumbers[$j], 
                        $predictedNumbers[$k]
                    ];
                }
            }
        }
        
        return $combinations;
    }

    /**
     * Cập nhật dataBet với các tổ hợp mới từ predictedNumbers
     * @param array $dataBet - Dữ liệu bet hiện tại
     * @param array $predictedNumbers - Mảng số dự đoán
     * @return array - Dữ liệu bet đã được cập nhật
     */
    private function updateDataBetWithPredicted($dataBet, $predictedNumbers)
    {
        // Sinh tất cả tổ hợp 3 số
        $combinations = $this->generateCombinationsFromPredicted($predictedNumbers);
        
        $updatedDataBet = [];
        
        // Cập nhật từng item trong dataBet với tổ hợp mới
        foreach ($combinations as $index => $combination) {
            // Nếu có item cũ thì giữ lại các thuộc tính khác, chỉ thay numberBet
            if (isset($dataBet[$index])) {
                $item = $dataBet[$index];
                $item['numberBet'] = $combination;
            } else {
                // Tạo item mới nếu không có
                $item = [
                    'countMiss' => 0,
                    'numberBet' => $combination,
                    'amount' => 10,
                    'status' => true,
                ];
            }
            
            $updatedDataBet[] = $item;
        }
        
        return $updatedDataBet;
    }

    public function handle(): int
    {
        $dayTest = $this->option('day') ?? 1;
        $port = $this->option('port') ?? self::PORT;
        $predictedCount = 60; // Số lượng số dự đoán (có thể điều chỉnh 60 hoặc 70)
        
        $service = app()->make(KennoHistoryService::class);
        $data = $service->getResultByDay($dayTest);

        $historyOfDay = [];
        foreach ($data as $itemH) {
            $dataConvert = explode('|', $itemH['resultRaw']);
            $historyOfDay[] = $dataConvert;
        }

        // Parse history
        $history = collect($data)->map(fn($r) => array_map('intval', explode('|', $r['resultRaw'])))->toArray(); // format = [[1,15,8,23, ...,,77],.. [2,17,8,23, ...,,66]]

        $a = 20; // kỳ bắt đầu
        $b = 119; // kỳ kết thúc
        $finalResult = array_fill(0, 45, 0);
        
        // Khởi tạo dataBet ban đầu
        $dataBet = [
            0 => [
                'countMiss'   => 0,
                'numberBet'   => [70,79,80],
                'amount'      => 10,
                'status'      => true,
            ],
            1 => [
                'countMiss'   => 0,
                'numberBet'   => [3,53,18],
                'amount'      => 10,
                'status'      => true,
            ],
        ];

        echo "=== BẮT ĐẦU THỐNG KÊ FAIL STREAK ===\n";
        echo "Ngày test: $dayTest, Port: $port\n";
        echo "Số lượng số dự đoán: $predictedCount\n";
        echo "Kỳ từ $a đến $b\n\n";

        for ($ky = $a; $ky <= $b; $ky++) {
            $result = $history[$ky] ?? null;
            if (!$result) continue;

            $rowPredict = array_slice($history, 0, $ky);

            // Gọi API Flask để lấy số dự đoán
            $response = Http::timeout(60)->post('http://127.0.0.1:' . $port . '/predict-10-number', [
                'draws_70' => $rowPredict,
            ]);

            if (!$response->ok()) {
                echo "Failed at kỳ $ky: " . $response->body() . "\n";
                continue;
            }

            $predicted = json_decode($response->body(), true);
            $predictedNumbers = $predicted['top10_miss_numbers'] ?? []; // format = [17,4,6,22,59, ... ,26,78,35]
            
            // ===== GEN LẠI TOÀN BỘ numberBet TỪ PREDICTED NUMBERS =====
            if (!empty($predictedNumbers) && count($predictedNumbers) >= $predictedCount) {
                // Lấy đúng số lượng cần thiết
                $predictedSlice = array_slice($predictedNumbers, 0, $predictedCount);
                
                // Gen lại dataBet với tổ hợp từ predicted numbers
                $dataBet = $this->updateDataBetWithPredicted($dataBet, $predictedSlice);
                
                if ($ky == $a) { // Log lần đầu
                    echo "Kỳ $ky: Đã gen lại " . count($dataBet) . " tổ hợp từ $predictedCount số dự đoán\n";
                    echo "Ví dụ 3 bộ đầu tiên:\n";
                    for ($i = 0; $i < min(3, count($dataBet)); $i++) {
                        echo "  " . ($i + 1) . ". [" . implode(',', $dataBet[$i]['numberBet']) . "]\n";
                    }
                    echo "\n";
                }
            } else {
                echo "Kỳ $ky: Không đủ predicted numbers (cần ít nhất $predictedCount), bỏ qua\n";
                continue;
            }
            // ===== KẾT THÚC GEN LẠI =====

            // Kiểm tra kết quả cho từng bộ số trong dataBet
            foreach ($dataBet as $key => $dataBetItem) {
                $numberBet = $dataBetItem['numberBet']; // Mảng 3 số [15, 1, 16]
                
                // Kiểm tra xem có ít nhất 1 số trùng với kết quả không
                $intersection = array_intersect($numberBet, $result);
                
                if (count($intersection) > 0) {
                    // Trùng ít nhất 1 số => countMiss + 1
                    $dataBetItem['countMiss'] += 1;
                } else {
                    // Không trùng số nào => thống kê fail streak và reset
                    if ($dataBetItem['countMiss'] > 0) {
                        $failStreak = $dataBetItem['countMiss'];
                        if ($failStreak < 45) {
                            $finalResult[$failStreak] += 1;
                        }
                        
                        if ($ky % 20 == 0) { // Log ít thôi để không spam
                            echo "Kỳ $ky: Bộ [" . implode(',', $numberBet) . "] trượt hoàn toàn sau $failStreak lần miss\n";
                        }
                    }
                    $dataBetItem['countMiss'] = 0; // Reset
                }

                $dataBet[$key] = $dataBetItem; // Cập nhật lại
            }

            // Log tiến trình
            if ($ky % 20 == 0) {
                $totalMiss = array_sum(array_column($dataBet, 'countMiss'));
                $maxMiss = max(array_column($dataBet, 'countMiss'));
                $avgMiss = $totalMiss / count($dataBet);
                echo "Kỳ $ky: Tổng " . count($dataBet) . " bộ số, Max miss = $maxMiss, Avg miss = " . round($avgMiss, 2) . "\n";
            }
        }

        // Hiển thị kết quả cuối cùng
        echo "\n=== KẾT QUẢ THỐNG KÊ FAIL STREAK ===\n";
        echo "Fail Streak | Số lần xuất hiện\n";
        echo "------------|------------------\n";
        
        $totalEvents = 0;
        for ($i = 1; $i < 45; $i++) {
            if ($finalResult[$i] > 0) {
                echo sprintf("%11d | %16d\n", $i, $finalResult[$i]);
                $totalEvents += $finalResult[$i];
            }
        }
        
        echo "------------|------------------\n";
        echo sprintf("%11s | %16d\n", "TỔNG", $totalEvents);

        if ($totalEvents > 0) {
            echo "\n=== PHÂN TÍCH CHI TIẾT ===\n";
            echo "Tổng số lần trượt hoàn toàn: $totalEvents\n";
            
            $maxCount = max($finalResult);
            $mostCommonStreak = array_search($maxCount, $finalResult);
            echo "Fail streak phổ biến nhất: $mostCommonStreak lần ($maxCount lần xuất hiện)\n";
            
            $totalStreaks = 0;
            for ($i = 1; $i < 45; $i++) {
                $totalStreaks += $i * $finalResult[$i];
            }
            $avgStreak = $totalStreaks / $totalEvents;
            echo "Fail streak trung bình: " . round($avgStreak, 2) . " lần\n";
        }

        dd($finalResult);
    }
}
