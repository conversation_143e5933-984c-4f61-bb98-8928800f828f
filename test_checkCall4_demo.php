<?php

/**
 * Demo test cho hàm checkCall4 - thống kê fail streak
 */

/**
 * <PERSON><PERSON> tất cả tổ hợp từ mảng predictedNumbers
 */
function generateCombinationsFromPredicted($predictedNumbers, $comboSize)
{
    $combinations = [];
    $count = count($predictedNumbers);
    
    if ($comboSize == 3) {
        for ($i = 0; $i < $count - 2; $i++) {
            for ($j = $i + 1; $j < $count - 1; $j++) {
                for ($k = $j + 1; $k < $count; $k++) {
                    $combinations[] = [$i, $j, $k];
                }
            }
        }
    }
    
    return $combinations;
}

/**
 * Demo hàm checkCall4 với dữ liệu giả lập
 */
function demoCheckCall4()
{
    echo "=== DEMO HÀM checkCall4 - THỐNG KÊ FAIL STREAK ===\n\n";
    
    $comboSize = 3;
    $sliceCount = 10; // Dùng 10 số để demo (thự<PERSON> <PERSON>ế là 60)
    
    // <PERSON><PERSON><PERSON> lậ<PERSON> dữ liệu predicted numbers và results cho 20 kỳ
    $demoData = [
        // [predictedNumbers, result]
        [[1,2,3,4,5,6,7,8,9,10], [1,5,9,12,15,18,21,24,27,30,33,36,39,42,45,48,51,54,57,60]],
        [[2,3,4,5,6,7,8,9,10,11], [2,6,10,13,16,19,22,25,28,31,34,37,40,43,46,49,52,55,58,61]],
        [[3,4,5,6,7,8,9,10,11,12], [3,7,11,14,17,20,23,26,29,32,35,38,41,44,47,50,53,56,59,62]],
        [[4,5,6,7,8,9,10,11,12,13], [4,8,12,15,18,21,24,27,30,33,36,39,42,45,48,51,54,57,60,63]],
        [[5,6,7,8,9,10,11,12,13,14], [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]], // Trúng nhiều
        [[6,7,8,9,10,11,12,13,14,15], [25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44]], // Trượt hoàn toàn
        [[7,8,9,10,11,12,13,14,15,16], [1,5,9,12,15,18,21,24,27,30,33,36,39,42,45,48,51,54,57,60]],
        [[8,9,10,11,12,13,14,15,16,17], [2,6,10,13,16,19,22,25,28,31,34,37,40,43,46,49,52,55,58,61]],
        [[9,10,11,12,13,14,15,16,17,18], [50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69]], // Trượt hoàn toàn
        [[10,11,12,13,14,15,16,17,18,19], [1,5,9,12,15,18,21,24,27,30,33,36,39,42,45,48,51,54,57,60]],
    ];
    
    // Khởi tạo biến thống kê
    $dataResult = array_fill(0, 45, 0);
    $comboIndices = [];
    $comboCountMiss = [];
    $comboCount = 0;
    
    echo "Bắt đầu demo với " . count($demoData) . " kỳ\n\n";
    
    foreach ($demoData as $ky => $data) {
        $predictedNumbers = $data[0];
        $result = $data[1];
        
        echo "=== KỲ " . ($ky + 1) . " ===\n";
        echo "Predicted: [" . implode(',', $predictedNumbers) . "]\n";
        echo "Result: [" . implode(',', array_slice($result, 0, 10)) . "...]\n";
        
        // Gen combo từ predicted numbers
        if (!empty($predictedNumbers) && count($predictedNumbers) >= $sliceCount) {
            $predictedSlice = array_slice($predictedNumbers, 0, $sliceCount);
            $newComboIndices = generateCombinationsFromPredicted($predictedSlice, $comboSize);
            
            if (empty($comboIndices) || count($newComboIndices) != $comboCount) {
                $comboIndices = $newComboIndices;
                $comboCount = count($comboIndices);
                $comboCountMiss = array_fill(0, $comboCount, 0);
                echo "Khởi tạo " . $comboCount . " tổ hợp\n";
            } else {
                $comboIndices = $newComboIndices;
            }
        }
        
        // Convert result sang format string để so sánh
        $resultFormatted = array_map(fn($n) => str_pad((string)$n, 2, '0', STR_PAD_LEFT), $result);
        
        $hitCount = 0;
        $missCount = 0;
        $resetCount = 0;
        
        // Kiểm tra từng bộ 3 số
        foreach ($comboIndices as $idx => $combo) {
            $triplet = array_map(fn($i) => str_pad((string)$predictedSlice[$i], 2, '0', STR_PAD_LEFT), $combo);
            $intersection = array_intersect($triplet, $resultFormatted);
            
            if (count($intersection) > 0) {
                // Có ít nhất 1 số trùng => countMiss + 1
                $comboCountMiss[$idx]++;
                $hitCount++;
            } else {
                // Cả 3 số đều trượt => thống kê và reset
                if ($comboCountMiss[$idx] > 0) {
                    $failStreak = $comboCountMiss[$idx];
                    if ($failStreak < 45) {
                        $dataResult[$failStreak]++;
                    }
                    echo "  Bộ [" . implode(',', $triplet) . "] trượt hoàn toàn sau $failStreak lần miss\n";
                    $resetCount++;
                }
                $comboCountMiss[$idx] = 0;
                $missCount++;
            }
        }
        
        echo "Hit: $hitCount, Miss: $missCount, Reset: $resetCount\n";
        echo "Max miss hiện tại: " . (empty($comboCountMiss) ? 0 : max($comboCountMiss)) . "\n\n";
    }
    
    // Hiển thị kết quả thống kê
    echo "=== KẾT QUẢ THỐNG KÊ FAIL STREAK ===\n";
    echo "Fail Streak | Số lần xuất hiện\n";
    echo "------------|------------------\n";
    
    $totalEvents = 0;
    for ($i = 1; $i < 45; $i++) {
        if ($dataResult[$i] > 0) {
            echo sprintf("%11d | %16d\n", $i, $dataResult[$i]);
            $totalEvents += $dataResult[$i];
        }
    }
    
    echo "------------|------------------\n";
    echo sprintf("%11s | %16d\n", "TỔNG", $totalEvents);
    
    if ($totalEvents > 0) {
        echo "\n=== PHÂN TÍCH ===\n";
        echo "Tổng số lần trượt hoàn toàn: $totalEvents\n";
        
        $maxCount = max($dataResult);
        $mostCommonStreak = array_search($maxCount, $dataResult);
        echo "Fail streak phổ biến nhất: $mostCommonStreak lần ($maxCount lần xuất hiện)\n";
        
        $totalStreaks = 0;
        for ($i = 1; $i < 45; $i++) {
            $totalStreaks += $i * $dataResult[$i];
        }
        $avgStreak = $totalStreaks / $totalEvents;
        echo "Fail streak trung bình: " . round($avgStreak, 2) . " lần\n";
    }
    
    return $dataResult;
}

echo "=== DEMO LOGIC checkCall4 ===\n\n";

echo "1. MỤC ĐÍCH:\n";
echo "- Thống kê fail streak của các bộ 3 số\n";
echo "- Tương tự truotXien4 nhưng để thống kê thay vì bet\n";
echo "- Đếm số lần miss liên tiếp trước khi trượt hoàn toàn\n\n";

echo "2. LOGIC:\n";
echo "- Mỗi kỳ: gen combo từ 60 số dự đoán\n";
echo "- Kiểm tra từng bộ 3 số:\n";
echo "  + Có ít nhất 1 số trùng => countMiss++\n";
echo "  + Cả 3 số đều trượt => thống kê countMiss và reset = 0\n";
echo "- Lưu kết quả vào dataResult[countMiss]++\n\n";

echo "3. DEMO VỚI DỮ LIỆU GIẢ LẬP:\n\n";

$result = demoCheckCall4();

echo "\n4. CÁCH SỬ DỤNG THỰC TẾ:\n";
echo "php artisan app:du-doan-10-number --day=1 --port=5049 --mode=checkCall4\n\n";

echo "5. KẾT QUẢ MONG ĐỢI:\n";
echo "- Bảng thống kê fail streak từ 1-44 lần\n";
echo "- Phân tích fail streak phổ biến nhất\n";
echo "- Fail streak trung bình\n";
echo "- Thống kê theo khoảng (1-5, 6-10, 11-15, ...)\n";

?>
