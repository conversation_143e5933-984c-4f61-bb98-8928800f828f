import math

def is_prime(n):
    """
    <PERSON><PERSON>m tra xem một số có phải là số nguyên tố hay không
    """
    if n < 2:
        return False
    if n == 2:
        return True
    if n % 2 == 0:
        return False

    # Chỉ cần kiểm tra đến căn bậc 2 của n
    for i in range(3, int(math.sqrt(n)) + 1, 2):
        if n % i == 0:
            return False
    return True

def generate_primes_up_to(limit):
    """
    Sinh tất cả số nguyên tố từ 2 đến limit
    """
    primes = []
    for num in range(2, limit + 1):
        if is_prime(num):
            primes.append(num)
    return primes

def generate_first_n_primes(n):
    """
    Sinh n số nguyên tố đầu tiên
    """
    primes = []
    num = 2
    while len(primes) < n:
        if is_prime(num):
            primes.append(num)
        num += 1
    return primes

def sieve_of_eratosthenes(limit):
    """
    Sàng Era<PERSON>thenes - cách hiệu quả để tìm tất cả số nguyên tố đến limit
    """
    if limit < 2:
        return []

    # Tạ<PERSON> mảng boolean, ban đầu tất cả đều True
    is_prime_arr = [True] * (limit + 1)
    is_prime_arr[0] = is_prime_arr[1] = False

    for i in range(2, int(math.sqrt(limit)) + 1):
        if is_prime_arr[i]:
            # Đánh dấu tất cả bội số của i
            for j in range(i * i, limit + 1, i):
                is_prime_arr[j] = False

    # Trả về danh sách các số nguyên tố
    return [i for i in range(2, limit + 1) if is_prime_arr[i]]

def prime_generator():
    """
    Generator sinh số nguyên tố vô hạn
    """
    yield 2
    num = 3
    while True:
        if is_prime(num):
            yield num
        num += 2  # Chỉ kiểm tra số lẻ

# Ví dụ sử dụng
if __name__ == "__main__":
    print("=== DEMO CÁC HÀMSINH SỐ NGUYÊN TỐ ===")

    # Kiểm tra số nguyên tố
    print(f"17 có phải số nguyên tố? {is_prime(17)}")
    print(f"18 có phải số nguyên tố? {is_prime(18)}")

    # Sinh số nguyên tố đến 50
    print(f"\nSố nguyên tố từ 2 đến 50: {generate_primes_up_to(50)}")

    # Sinh 10 số nguyên tố đầu tiên
    print(f"\n10 số nguyên tố đầu tiên: {generate_first_n_primes(10)}")

    # Sử dụng sàng Eratosthenes
    print(f"\nSố nguyên tố đến 30 (sàng Eratosthenes): {sieve_of_eratosthenes(30)}")

    # Sử dụng generator
    print("\n5 số nguyên tố đầu tiên từ generator:")
    gen = prime_generator()
    for i in range(5):
        print(next(gen), end=" ")
    print()
