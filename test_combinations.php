<?php

/**
 * File test để demo hàm sinh tổ hợp 3 số từ 60 số
 */

/**
 * <PERSON>h tất cả tổ hợp 3 số từ mảng numberDuDoan
 * @param array $numberDuDoan - Mảng 60 số
 * @return array - <PERSON><PERSON><PERSON> các tổ hợp 3 số (format giống key3.txt)
 */
function generateCombinations($numberDuDoan)
{
    $combinations = [];
    $count = count($numberDuDoan);

    // Sinh tất cả tổ hợp 3 số theo vị trí
    for ($i = 0; $i < $count - 2; $i++) {
        for ($j = $i + 1; $j < $count - 1; $j++) {
            for ($k = $j + 1; $k < $count; $k++) {
                // Format giống key3.txt: ["07","77","62"]
                $combination = [
                    str_pad($numberDuDoan[$i], 2, '0', STR_PAD_LEFT),
                    str_pad($numberDuDoan[$j], 2, '0', STR_PAD_LEFT),
                    str_pad($numberDuDoan[$k], 2, '0', STR_PAD_LEFT)
                ];
                $combinations[] = $combination;
            }
        }
    }

    return $combinations;
}

/**
 * Cập nhật dataBet với các tổ hợp mới từ numberDuDoan
 * @param array $dataBet - Dữ liệu bet hiện tại
 * @param array $numberDuDoan - Mảng 60 số dự đoán
 * @return array - Dữ liệu bet đã được cập nhật
 */
function updateDataBetWithCombinations($dataBet, $numberDuDoan)
{
    // Sinh tất cả tổ hợp 3 số
    $combinations = generateCombinations($numberDuDoan);
    
    $updatedDataBet = [];
    
    // Cập nhật từng item trong dataBet với tổ hợp mới
    foreach ($combinations as $index => $combination) {
        // Nếu có item cũ thì giữ lại các thuộc tính khác, chỉ thay numberBet
        if (isset($dataBet[$index])) {
            $item = $dataBet[$index];
            $item['numberBet'] = $combination;
        } else {
            // Tạo item mới nếu không có
            $item = [
                'countMiss' => 0,
                'numberBet' => $combination,
                'amount' => 10,
                'status' => true,
            ];
        }
        
        $updatedDataBet[] = $item;
    }
    
    return $updatedDataBet;
}

// ===== DEMO =====
echo "=== DEMO SINH TỔ HỢP 3 SỐ TỪ 60 SỐ ===\n\n";

// Tạo mảng 60 số mẫu (từ 1 đến 60)
$numberDuDoan = [];
for ($i = 1; $i <= 60; $i++) {
    $numberDuDoan[] = $i;
}

echo "Mảng 60 số dự đoán (mẫu):\n";
echo implode(', ', $numberDuDoan) . "\n\n";

// Sinh tổ hợp
$combinations = generateCombinations($numberDuDoan);

echo "Tổng số tổ hợp 3 số được sinh ra: " . count($combinations) . "\n\n";

// Hiển thị 10 tổ hợp đầu tiên
echo "10 tổ hợp đầu tiên (format giống key3.txt):\n";
for ($i = 0; $i < min(10, count($combinations)); $i++) {
    echo ($i + 1) . ". [\"" . implode('","', $combinations[$i]) . "\"]\n";
}

echo "\n10 tổ hợp cuối cùng:\n";
$start = max(0, count($combinations) - 10);
for ($i = $start; $i < count($combinations); $i++) {
    echo ($i + 1) . ". [\"" . implode('","', $combinations[$i]) . "\"]\n";
}

// Test với dataBet mẫu
echo "\n=== TEST CẬP NHẬT DATABET ===\n";

$dataBetSample = [
    [
        'countMiss' => 5,
        'numberBet' => ['01', '02', '03'], // Format giống key3.txt
        'amount' => 10,
        'status' => true,
    ],
    [
        'countMiss' => 2,
        'numberBet' => ['04', '05', '06'], // Format giống key3.txt
        'amount' => 15,
        'status' => false,
    ]
];

echo "DataBet mẫu trước khi cập nhật:\n";
print_r($dataBetSample);

// Sử dụng mảng 10 số để test (để kết quả ngắn gọn hơn)
$numberDuDoanTest = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
$updatedDataBet = updateDataBetWithCombinations($dataBetSample, $numberDuDoanTest);

echo "\nDataBet sau khi cập nhật với 10 số (tổng " . count($updatedDataBet) . " tổ hợp):\n";
echo "5 item đầu tiên:\n";
for ($i = 0; $i < min(5, count($updatedDataBet)); $i++) {
    echo "Item " . ($i + 1) . ":\n";
    echo "  - numberBet: [\"" . implode('","', $updatedDataBet[$i]['numberBet']) . "\"] (format giống key3.txt)\n";
    echo "  - countMiss: " . $updatedDataBet[$i]['countMiss'] . "\n";
    echo "  - amount: " . $updatedDataBet[$i]['amount'] . "\n";
    echo "  - status: " . ($updatedDataBet[$i]['status'] ? 'true' : 'false') . "\n\n";
}

// Tính toán số tổ hợp với 60 số
$totalCombinations = 0;
for ($i = 0; $i < 60 - 2; $i++) {
    for ($j = $i + 1; $j < 60 - 1; $j++) {
        for ($k = $j + 1; $k < 60; $k++) {
            $totalCombinations++;
        }
    }
}

echo "=== THÔNG TIN QUAN TRỌNG ===\n";
echo "Với 60 số, tổng số tổ hợp 3 số sẽ là: " . $totalCombinations . " tổ hợp\n";
echo "Công thức: C(60,3) = 60!/(3!*(60-3)!) = " . (60*59*58)/(3*2*1) . "\n";
echo "Điều này có nghĩa là dataBet sẽ có " . $totalCombinations . " items sau khi cập nhật.\n";

?>
