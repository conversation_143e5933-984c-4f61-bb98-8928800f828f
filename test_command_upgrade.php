<?php

/**
 * Demo test cho TestDuDoan10soCommand đã đượ<PERSON> nâng cấp
 */

/**
 * <PERSON><PERSON> tất cả tổ hợp từ mảng predictedNumbers
 * @param array $predictedNumbers - <PERSON><PERSON><PERSON> 60-70 số dự đoán
 * @param int $comboSize - Kí<PERSON> thước tổ hợp (3 hoặc 4)
 * @return array - Mảng các tổ hợp index
 */
function generateCombinationsFromPredicted($predictedNumbers, $comboSize)
{
    $combinations = [];
    $count = count($predictedNumbers);
    
    if ($comboSize == 3) {
        // Sinh tất cả tổ hợp 3 số theo vị trí
        for ($i = 0; $i < $count - 2; $i++) {
            for ($j = $i + 1; $j < $count - 1; $j++) {
                for ($k = $j + 1; $k < $count; $k++) {
                    $combinations[] = [$i, $j, $k]; // Trả về index để tương thích với code cũ
                }
            }
        }
    } elseif ($comboSize == 4) {
        // <PERSON>h tất cả tổ hợp 4 số theo vị trí
        for ($i = 0; $i < $count - 3; $i++) {
            for ($j = $i + 1; $j < $count - 2; $j++) {
                for ($k = $j + 1; $k < $count - 1; $k++) {
                    for ($l = $k + 1; $l < $count; $l++) {
                        $combinations[] = [$i, $j, $k, $l]; // Trả về index để tương thích với code cũ
                    }
                }
            }
        }
    }
    
    return $combinations;
}

// Hàm combinations cũ (để so sánh)
function combinations(array $array, int $r): array {
    $results = [];
    $n = count($array);
    if ($r > $n) return [];

    $indexes = range(0, $r - 1);
    while (true) {
        $results[] = array_map(fn($i) => $array[$i], $indexes);

        for ($i = $r - 1; $i >= 0; $i--) {
            if ($indexes[$i] !== $i + $n - $r) break;
        }

        if ($i < 0) break;

        $indexes[$i]++;
        for ($j = $i + 1; $j < $r; $j++) {
            $indexes[$j] = $indexes[$j - 1] + 1;
        }
    }
    return $results;
}

echo "=== DEMO NÂNG CẤP TestDuDoan10soCommand ===\n\n";

// Test với 10 số để demo (thực tế sẽ là 60-70 số)
$predictedNumbers = [5, 12, 23, 34, 45, 56, 67, 78, 89, 90];
$comboSize = 3;
$sliceCount = count($predictedNumbers);

echo "1. CÁCH CŨ (từ range):\n";
$oldComboIndices = combinations(range(0, $sliceCount - 1), $comboSize);
echo "Số lượng tổ hợp: " . count($oldComboIndices) . "\n";
echo "5 tổ hợp đầu tiên:\n";
for ($i = 0; $i < min(5, count($oldComboIndices)); $i++) {
    $combo = $oldComboIndices[$i];
    $values = array_map(fn($idx) => $predictedNumbers[$idx], $combo);
    echo "  " . ($i + 1) . ". Index [" . implode(', ', $combo) . "] => Values [" . implode(', ', $values) . "]\n";
}

echo "\n2. CÁCH MỚI (từ predictedNumbers):\n";
$newComboIndices = generateCombinationsFromPredicted($predictedNumbers, $comboSize);
echo "Số lượng tổ hợp: " . count($newComboIndices) . "\n";
echo "5 tổ hợp đầu tiên:\n";
for ($i = 0; $i < min(5, count($newComboIndices)); $i++) {
    $combo = $newComboIndices[$i];
    $values = array_map(fn($idx) => $predictedNumbers[$idx], $combo);
    echo "  " . ($i + 1) . ". Index [" . implode(', ', $combo) . "] => Values [" . implode(', ', $values) . "]\n";
}

echo "\n3. SO SÁNH:\n";
echo "✅ Cách cũ và cách mới cho kết quả giống nhau: " . (json_encode($oldComboIndices) === json_encode($newComboIndices) ? 'TRUE' : 'FALSE') . "\n";

echo "\n4. TEST VỚI 60 SỐ:\n";
$predictedNumbers60 = range(1, 60);
$combo60Old = combinations(range(0, 59), 3);
$combo60New = generateCombinationsFromPredicted($predictedNumbers60, 3);

echo "Với 60 số:\n";
echo "- Cách cũ: " . count($combo60Old) . " tổ hợp\n";
echo "- Cách mới: " . count($combo60New) . " tổ hợp\n";
echo "- Kết quả giống nhau: " . (count($combo60Old) === count($combo60New) ? 'TRUE' : 'FALSE') . "\n";

echo "\n5. TEST VỚI 70 SỐ:\n";
$predictedNumbers70 = range(1, 70);
$combo70Old = combinations(range(0, 69), 3);
$combo70New = generateCombinationsFromPredicted($predictedNumbers70, 3);

echo "Với 70 số:\n";
echo "- Cách cũ: " . count($combo70Old) . " tổ hợp\n";
echo "- Cách mới: " . count($combo70New) . " tổ hợp\n";
echo "- Kết quả giống nhau: " . (count($combo70Old) === count($combo70New) ? 'TRUE' : 'FALSE') . "\n";

echo "\n6. TEST VỚI COMBO SIZE 4:\n";
$combo4Old = combinations(range(0, 9), 4);
$combo4New = generateCombinationsFromPredicted($predictedNumbers, 4);

echo "Với 10 số, combo size 4:\n";
echo "- Cách cũ: " . count($combo4Old) . " tổ hợp\n";
echo "- Cách mới: " . count($combo4New) . " tổ hợp\n";
echo "- Kết quả giống nhau: " . (count($combo4Old) === count($combo4New) ? 'TRUE' : 'FALSE') . "\n";

echo "\n7. DEMO LOGIC NÂNG CẤP:\n";
echo "```php\n";
echo "// ===== NÂNG CẤP: GEN LẠI COMBO TỪ PREDICTED NUMBERS =====\n";
echo "// Chỉ gen lại khi có predictedNumbers với số lượng đủ\n";
echo "if (!empty(\$predictedNumbers) && count(\$predictedNumbers) >= \$sliceCount) {\n";
echo "    // Lấy đúng số lượng cần thiết\n";
echo "    \$predictedSlice = array_slice(\$predictedNumbers, 0, \$sliceCount);\n";
echo "    \n";
echo "    // Gen lại combo từ predicted numbers\n";
echo "    \$newComboIndices = \$this->generateCombinationsFromPredicted(\$predictedSlice, \$comboSize);\n";
echo "    \n";
echo "    // Cập nhật nếu số lượng combo thay đổi\n";
echo "    if (count(\$newComboIndices) != \$comboCount) {\n";
echo "        \$comboIndices = \$newComboIndices;\n";
echo "        \$comboCount = count(\$comboIndices);\n";
echo "        \$comboLastMiss = array_fill(0, \$comboCount, null);\n";
echo "        Log::info(\"Kỳ \$ky: Đã gen lại \" . \$comboCount . \" tổ hợp từ \" . \$sliceCount . \" số dự đoán\");\n";
echo "    } else {\n";
echo "        \$comboIndices = \$newComboIndices;\n";
echo "    }\n";
echo "}\n";
echo "// ===== KẾT THÚC NÂNG CẤP =====\n";
echo "```\n";

echo "\n8. KẾT LUẬN:\n";
echo "✅ Hàm mới tương thích hoàn toàn với code cũ\n";
echo "✅ Có thể điều chỉnh sliceCount từ 60 lên 70\n";
echo "✅ Hỗ trợ cả combo size 3 và 4\n";
echo "✅ Gen lại combo mỗi kỳ từ predictedNumbers\n";
echo "✅ Logic cũ vẫn hoạt động bình thường\n";

?>
