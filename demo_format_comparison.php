<?php

/**
 * Demo so sánh format giữa key3.txt và code mới
 */

echo "=== SO SÁNH FORMAT ===\n\n";

// Format trong key3.txt
echo "1. FORMAT TRONG key3.txt:\n";
$key3Format = [
    ["07","77","62"],
    ["07","77","19"], 
    ["07","77","49"],
    ["07","77","51"]
];

echo "[\n";
foreach ($key3Format as $item) {
    echo '  ["' . implode('","', $item) . '"],' . "\n";
}
echo "  ...\n]\n\n";

// Format trong dataBet sau khi load từ key3.txt
echo "2. FORMAT TRONG dataBet (sau khi load từ key3.txt):\n";
echo "[\n";
echo "  [\n";
echo "    'countMiss' => 0,\n";
echo "    'numberBet' => ['07', '77', '62'],  // Mảng string 2 chữ số\n";
echo "    'amount' => 10,\n";
echo "    'status' => true,\n";
echo "  ],\n";
echo "  [\n";
echo "    'countMiss' => 0,\n";
echo "    'numberBet' => ['07', '77', '19'],  // Mảng string 2 chữ số\n";
echo "    'amount' => 10,\n";
echo "    'status' => true,\n";
echo "  ],\n";
echo "  ...\n";
echo "]\n\n";

// Format code mới sinh ra
echo "3. FORMAT CODE MỚI SINH RA:\n";

// Mô phỏng numberDuDoan
$numberDuDoan = [7, 77, 62, 19, 49, 51];

// Sinh tổ hợp
function generateCombinations($numberDuDoan)
{
    $combinations = [];
    $count = count($numberDuDoan);
    
    for ($i = 0; $i < $count - 2; $i++) {
        for ($j = $i + 1; $j < $count - 1; $j++) {
            for ($k = $j + 1; $k < $count; $k++) {
                $combination = [
                    str_pad($numberDuDoan[$i], 2, '0', STR_PAD_LEFT),
                    str_pad($numberDuDoan[$j], 2, '0', STR_PAD_LEFT),
                    str_pad($numberDuDoan[$k], 2, '0', STR_PAD_LEFT)
                ];
                $combinations[] = $combination;
            }
        }
    }
    
    return $combinations;
}

$newCombinations = generateCombinations($numberDuDoan);

echo "Từ numberDuDoan = [7, 77, 62, 19, 49, 51] sinh ra:\n";
echo "[\n";
foreach (array_slice($newCombinations, 0, 5) as $combo) {
    echo '  ["' . implode('","', $combo) . '"],' . "\n";
}
echo "  ... (tổng " . count($newCombinations) . " tổ hợp)\n";
echo "]\n\n";

// So sánh với format cũ
echo "4. SO SÁNH VỚI FORMAT CŨ:\n";
echo "✅ ĐÚNG: numberBet là mảng ['07', '77', '62']\n";
echo "✅ ĐÚNG: Các số đều có format 2 chữ số dạng string\n";
echo "✅ ĐÚNG: Cấu trúc dataBet giữ nguyên, chỉ thay numberBet\n\n";

// Demo cách sử dụng trong code thực tế
echo "5. DEMO TRONG CODE THỰC TẾ:\n";

// Mô phỏng dataBet cũ
$oldDataBet = [
    [
        'countMiss' => 5,
        'numberBet' => ['07', '77', '62'],
        'amount' => 10,
        'status' => true,
    ],
    [
        'countMiss' => 2,
        'numberBet' => ['07', '77', '19'],
        'amount' => 15,
        'status' => false,
    ]
];

echo "DataBet cũ (từ key3.txt):\n";
print_r($oldDataBet);

// Mô phỏng numberDuDoan 60 số (chỉ lấy 6 số để demo)
$numberDuDoan60 = [1, 2, 3, 4, 5, 6]; // Thực tế sẽ có 60 số

function updateDataBetWithCombinations($dataBet, $numberDuDoan)
{
    $combinations = generateCombinations($numberDuDoan);
    $updatedDataBet = [];
    
    foreach ($combinations as $index => $combination) {
        if (isset($dataBet[$index])) {
            $item = $dataBet[$index];
            $item['numberBet'] = $combination;
        } else {
            $item = [
                'countMiss' => 0,
                'numberBet' => $combination,
                'amount' => 10,
                'status' => true,
            ];
        }
        
        $updatedDataBet[] = $item;
    }
    
    return $updatedDataBet;
}

$newDataBet = updateDataBetWithCombinations($oldDataBet, $numberDuDoan60);

echo "\nDataBet mới (sau khi gen từ 6 số):\n";
echo "Tổng số items: " . count($newDataBet) . "\n";
echo "3 items đầu tiên:\n";
for ($i = 0; $i < min(3, count($newDataBet)); $i++) {
    echo "Item " . ($i + 1) . ":\n";
    echo "  numberBet: [\"" . implode('","', $newDataBet[$i]['numberBet']) . "\"]\n";
    echo "  countMiss: " . $newDataBet[$i]['countMiss'] . "\n";
    echo "  amount: " . $newDataBet[$i]['amount'] . "\n";
    echo "  status: " . ($newDataBet[$i]['status'] ? 'true' : 'false') . "\n\n";
}

echo "6. KẾT LUẬN:\n";
echo "✅ Format numberBet hoàn toàn giống với key3.txt\n";
echo "✅ Cấu trúc dataBet được giữ nguyên\n";
echo "✅ Logic cũ vẫn hoạt động bình thường\n";
echo "✅ Chỉ thay đổi cách sinh numberBet từ cố định sang dynamic\n";

?>
