<?php

namespace App\Http\Controllers;

use App\Jobs\RunBetX4he15ThreeJob15;
use App\Services\KennoHistoryService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TruotType15New3ThreeKeyController extends Controller
{
//    use ManageBetTrait;
    CONST TAKE_PROFIT = 11200000; // cài đặt chốt lãi
    CONST PORT = '5049';

    public function getToken()
    {
        return '4-e7156ef157fb4e9e8c5cfe0b5c4d4478';
    }

    /**
     * <PERSON>h tất cả tổ hợp 3 số từ mảng numberDuDoan
     * @param array $numberDuDoan - Mảng 60 số
     * @return array - Mảng các tổ hợp 3 số (format giống key3.txt)
     */
    private function generateCombinations($numberDuDoan)
    {
        $combinations = [];
        $count = count($numberDuDoan);

        // <PERSON>h tất cả tổ hợp 3 số theo vị trí
        for ($i = 0; $i < $count - 2; $i++) {
            for ($j = $i + 1; $j < $count - 1; $j++) {
                for ($k = $j + 1; $k < $count; $k++) {
                    // Format giống key3.txt: ["07","77","62"]
                    $combination = [
                        str_pad($numberDuDoan[$i], 2, '0', STR_PAD_LEFT),
                        str_pad($numberDuDoan[$j], 2, '0', STR_PAD_LEFT),
                        str_pad($numberDuDoan[$k], 2, '0', STR_PAD_LEFT)
                    ];
                    $combinations[] = $combination;
                }
            }
        }

        return $combinations;
    }

    /**
     * Cập nhật dataBet với các tổ hợp mới từ numberDuDoan
     * @param array $dataBet - Dữ liệu bet hiện tại
     * @param array $numberDuDoan - Mảng 60 số dự đoán
     * @return array - Dữ liệu bet đã được cập nhật
     */
    private function updateDataBetWithCombinations($dataBet, $numberDuDoan)
    {
        // Sinh tất cả tổ hợp 3 số
        $combinations = $this->generateCombinations($numberDuDoan);
        
        $updatedDataBet = [];
        
        // Cập nhật từng item trong dataBet với tổ hợp mới
        foreach ($combinations as $index => $combination) {
            // Nếu có item cũ thì giữ lại các thuộc tính khác, chỉ thay numberBet
            if (isset($dataBet[$index])) {
                $item = $dataBet[$index];
                $item['numberBet'] = $combination;
            } else {
                // Tạo item mới nếu không có
                $item = [
                    'countMiss' => 0,
                    'numberBet' => $combination,
                    'amount' => 10,
                    'status' => true,
                ];
            }
            
            $updatedDataBet[] = $item;
        }
        
        return $updatedDataBet;
    }

    // TO DO chặn khi số vé đôt biến: nếu vé > 20 1 kỳ => chọn random max 20 vé. kiểm soát số đột biến
    // api chọn số từ predict python => chọn 10 số dự đoán => lưu cache theo id kỳ khoảng 600s => ghép 5 vé chọn từ ai chọn số để ghép thành
    // foreach đếm nếu trùng số trogn bộ key thì continue else countTicket++ đến khi đủ số lương config maxTicketCombo = 5
    public function truotXien4()
    {
//        return true;
        ini_set('memory_limit', -1);
        $keyCacheDataBetNode = 'dataBetNode';
        // chay tu sang
        $time = now()->copy()->format('H');

        $limitTime = 8;
        if ((int)$time < $limitTime || (int)$time > 22) {
            return true;
        }
        sleep(3);
        // Mỗi kỳ chỉ bẹt 1 lần

        $responseListResult = $this->currentListResultKenno();
        $resultPre          = $responseListResult['resultRaw']; // kq ky trc
        $roundId            = $responseListResult['id'];
        $periodId           = $responseListResult['periodId'];


        $cacheLock = Cache::lock($this->cacheOf($roundId), 1000);
        if (!$cacheLock->get()) {
            Log::info('đã bẹt r');
            return  "đã bẹt r!";
        }
        $takeProfit = $this->getStatusProfit();

        if ($takeProfit) {
            echo 'da chot lai';
            return true;
        }

        $numberDuDoan = [];
        if ((int)$time < $limitTime + 1) { // chi phu hop vs time lon hon 18, neu nho hon thi chi giu lai trong else

        } else {
            // lich su cac ky trc do
            $resultHistory = $this->getResult();
            // call api lấy 10 số dự đoán
            $numberDuDoan = $this->duDoan($resultHistory);
        }


        // config gấp thếp và số tiền gấp thếp
        $amountCustom = [
            16 => 10,
            17 => 14,
            18 => 19,
            19 => 25,
            20 => 30,
            21 => 34,
            22 => 38,
            23 => 42,
        ];

        $totalTicketBet = [
            16 => 3,
            17 => 4,
            18 => 5,
            19 => 6,
            20 => 7,
            21 => 7,
            22 => 7,
            23 => 7,
        ];

        // lấy data trong cache
        $dataBet = Cache::get($this->cacheOf($keyCacheDataBetNode), null);

        // khoi tao cho bet
        if ($dataBet == null) {
            $dataBet = $this->getDataBet();
        }
        
        // ===== NÂNG CẤP: GEN LẠI TOÀN BỘ SỐ TRONG ARRAY numberBet =====
        // Chỉ gen lại khi có numberDuDoan (60 số)
        if (!empty($numberDuDoan) && count($numberDuDoan) == 60) {
            $dataBet = $this->updateDataBetWithCombinations($dataBet, $numberDuDoan);
            Log::info('Đã gen lại ' . count($dataBet) . ' tổ hợp từ 60 số dự đoán');
        }
        // ===== KẾT THÚC NÂNG CẤP =====
        
        // stop = balance

        // array ket qua ky trc
        $arrayResultPre     =  explode('|', $resultPre);

        // setting bắt đầu sau bao nhiêu lần toạ0c thì bẹt
        $numberKeyBet       = 16; // sau 5 lần toạc thì bẹt 10k lần thứ 5+1 = 6 sẽ bẹt [3,10] [6-13] [7-14] [8-15] [9-16]
        $stopRange          = 7; // default =7 ĐÁNH 8 HÀNG THÌ CHỌN 7; N LÀ N-1; NẾU ĐÁNH 12 HÀNG THÌ CHỌN 11
        try {
            // kiếm tra kết quả
            $keyXien4 = 3; // key nếu bộ 3 số thì để 3 còn bộ 4 thì để 4
            $maxLimitBetFirst = 30; // MAX PAIR THREE BET số bộ số đạt ngưỡng bẹt sẽ được giới hạn, chỉ được phép bẹt 20 bộ số trong ngưỡng, tránh dẹo thanh khoản
            foreach ($dataBet as $key => $itemDataBet) {
                $arrayBet       = $itemDataBet['numberBet'];
                $arrayDiffKQ    = array_diff($arrayBet, $arrayResultPre);

                if (count($arrayDiffKQ) != $keyXien4) { // toạch xiên 3 cua bo 3 key
                    $itemDataBet['countMiss']  += 1;

                    // xem count miss đạt ngưỡng bẹt thì check total tiket
                    if ($itemDataBet['countMiss'] == $numberKeyBet) {
                        $maxLimitBetFirst--; // toongr bejt cho ngưỡng tối đa là 20 bộ 3 key 1 lần
                        // nếu sử dụng hết số lượt thì set = 0 hoặc fail
                        if ($maxLimitBetFirst < 0) {
                            $itemDataBet['countMiss'] = 0;

                        }
                    }
                } else { // trúng xiên 4
                    // reset count vi da trung xien truot 4
                    $itemDataBet['countMiss'] = 0;
                }

                // bắn thông báo khi fail
                if ($itemDataBet['countMiss'] == $numberKeyBet + $stopRange + 1) { // Ngoài ra điều kiện còn có sau 9h mà vẫn <5 lần thì dừng
                    pushNoticeTele('FAIL TEAM 15 three : ' . implode(',', $itemDataBet['numberBet']) . ' lúc ' . now()->copy()->format('d/m H:i:s'));
                }

                // thời gian tắt khoảng 18h - 19h tối
                if ((int)$time >= 21 && ($itemDataBet['countMiss'] < $numberKeyBet || $itemDataBet['countMiss'] > $stopRange + $numberKeyBet)) {
                    $itemDataBet['status'] = false; // vượt qua ngưỡng 13 lần sai thì reset về 0 Tổng 8 lần B sai
                }

                // nếu Fail 5 lần rồi, sang lần thứ 6 thì B => trong khoảng này ms dc vào bẹt
                if ($itemDataBet['status'] && $itemDataBet['countMiss'] >= $numberKeyBet && $itemDataBet['countMiss'] <= $stopRange + $numberKeyBet) { // = 6
                    // bet với NODE này

                    // tính toán amount
                    $amountCountTime    = $itemDataBet['countMiss'] - $numberKeyBet;
                    if (isset($amountCustom[$itemDataBet['countMiss']])) {
                        $amount =  $amountCustom[$itemDataBet['countMiss']];
                    } else {
                        $amount             = (int)floor((1 ** $amountCountTime) * $itemDataBet['amount']);
                    }

                    $fullNumberBet      = implode(',', $itemDataBet['numberBet']); // string


                    // bet
                    if ($itemDataBet['status']) { // nếu bộ số đã từng vượt 14 lần thì stop k cho chạy hay bẹt nữa

                        $countTicket = 0; // count xem trong forreach count dc mấy vé cho bộ số này r, Nếu đủ $totalTicket vé r thì dừng
                        $totalTicket = $totalTicketBet[$itemDataBet['countMiss']] ?? 5; // ghép với mấy số trong dự đoán,

                        // ghép 5 số trong bộ dự đoán để bẹt
                        foreach ($numberDuDoan as $itemNumberDuDoan) {
                            // kiem tra xem cos nawm trong bo 3 key k
                            if (in_array($itemNumberDuDoan, $itemDataBet['numberBet'])) {
                                continue;
                            }
                            $countTicket++;
                            $fullNumberBetFinal = $fullNumberBet.",$itemNumberDuDoan";
                            RunBetX4he15ThreeJob15::dispatch($amount, $fullNumberBetFinal, $responseListResult)->onQueue('three15');
                            if ($countTicket == $totalTicket) { // đạt 5 ticket => chim én
                                break;
                            }

                        }

                    }
                }

                $dataBet[$key] = $itemDataBet;
            }
            Log::info("Round $roundId Bet Ticket number Con lai " . $maxLimitBetFirst );

            // xử lý xong lưu cache
            Cache::put($this->cacheOf($keyCacheDataBetNode), $dataBet, 90000);
            Log::info('bẹt xong');
            return 'bet done';
        } catch (\Exception $exception) {
            Log::error('Lỗi B ' . $exception->getMessage());
            // ban notice telegrame
            return 'B fail';
        }
    }

    private function duDoan($rounds70)
    {
        try {
            $port = self::PORT;
            $response = Http::timeout(60)->post('http://127.0.0.1:'.$port.'/predict-10-number', [
                'draws_70' => $rounds70,
            ]);

            $predicted = json_decode($response->body(), true);
            $predictedNumbers = $predicted['top10_miss_numbers'] ?? [];
            $formatted = array_map(fn($n) => str_pad($n, 2, '0', STR_PAD_LEFT), $predictedNumbers);

            return $formatted;
        } catch (\Exception $exception) {
            Log::error('Lỗi chưa bật server python ');
            return [];
        }
    }

    public function betxien4($amount, $numberBet, $response)
    {
        sleep(1);
        $token = $this->getToken();

        $periodId   = $response['periodId']; // vietlottTicket
        $roundId    = $response['id'];

        $periodId   = $this->periodId($periodId);
        $roundId    = $roundId + 1;

        $headers = [
            'Content-Type' => 'application/json'
        ];
        $url = 'https://api-knlt.gamingon.net/api/v1/bet';
        $body = '{
          "token": "'. $token . '",
          "betType": "TRUOT_XIEN_4",
          "amount": '. $amount . ',
          "roundId": ' . $roundId . ',
          "vietlottTicket": "' . $periodId . '",
          "betNumbers": "' . $numberBet . '"
        }';

        $arrayBody = json_decode($body, true);

        $response = Http::withHeaders($headers)->post($url, $arrayBody);
        $this->returnResponsive($response, $numberBet);
        echo $response;
        echo "</br>";
    }

    private function getResult($day = 0)
    {
        $service = app()->make(KennoHistoryService::class);
        $data = $service->getResultByDayBet($day);
        $dataConvert = $data[0];
        $startAtRaw = $dataConvert['startAt'];
        $startTime = \Carbon\Carbon::parse($startAtRaw);

        $historyOfDay = [];
        foreach ($data as $itemH) {
            $dataConvert = explode('|', $itemH['resultRaw']);
            $historyOfDay[] = $dataConvert;
        }

        // Parse history
        $history = collect($data)->map(fn($r) => array_map('intval', explode('|', $r['resultRaw'])))->toArray();

        return $history;
    }

    public function currentListResultKenno($day = 1)
    {
        $from = now()->copy()->format('Y/m/d');
        $to   = now()->copy()->addDays($day)->format('Y/m/d');
        $url = 'https://api-knlt.gamingon.net/api/v1/rounds/1?limit=3&status=ENDED&sort=DESC&page=0&from_date=' . $from . '&to_date=' . $to;


        $headers = [
            'Content-Type' => 'application/json'
        ];

        $cachekey = 'currentListResultKenno';
        $response = Cache::get($cachekey, null);

        if ($response == null) {
            $response = Http::withHeaders($headers)->get($url);
            $response = json_decode($response, true);
            // chay tu sang
            $time = now()->copy()->format('H');

            if ((int)$time < 6) {
                return true;
            }
            $response   = $response['content'][0];

            Cache::put($cachekey, $response, 35);
        }

        return $response;
    }

    public function setTakeProfit($balance = 0) // balance account hien tai
    {
        $takeProfit = self::TAKE_PROFIT;

        $statusProfit = $this->getStatusProfit();
        if ($balance >= $takeProfit && !$statusProfit) {
            Cache::put($this->cacheProfit(), true, 86300);
            pushNoticeTele('TAKE PROFIT SUCCESSFULLY !!!!!');
            return true;
        }

        return false;
    }

    public function returnResponsive($response, $numberBet)
    {
        try {
            if ($response->successful()) {
                // Trả về kết quả từ API nếu thành công
                Log::info('BẸT THÀNH CÔNG');
            } else {
                // Trả về lỗi nếu request không thành công
                Log::error('BET FAIL ' . $numberBet, $response);
                pushNoticeTele('BET FAIL');
            }
        } catch (\Exception $exception) {
            Log::error($exception->getMessage());
        }
    }

    public function periodId($periodId)
    {
        $periodId = $periodId + 1;
        $periodId = "0" . $periodId;

        return $periodId;
    }

    public function cacheOf($keyOf): string
    {
        return 'threee_15_' . $keyOf;
    }

    public function getData($day = 0, $page = 0, $sort = 'ASC') // page tu` 0
    {

        $day    = (int)$day;

        $from = now()->copy()->addDays(-$day)->endOfDay()->format('Y/m/d');
        $to   = now()->copy()->addDays(1 - $day)->endOfDay()->format('Y/m/d');

        $url = 'https://api-knlt.gamingon.net/api/v1/rounds/1?limit=30&status=ENDED&sort='.$sort.'&page='.$page.'&from_date=' . $from . '&to_date=' . $to;

        $headers = [
            'Content-Type' => 'application/json'
        ];
        $response = Http::withHeaders($headers)->get($url);
        $response = json_decode($response, true);
        // chay tu sang
        $time = now()->copy()->format('H');

        $response   = $response['content'];

        return $response;
    }

    // quan trong
    public function getDataBet() {
        $filePath   = storage_path('app/key3.txt');
        $x          = file_get_contents($filePath);
        $data       = json_decode($x);

        $arrayTempleBig = $data;
        $arrayResult = [];
        foreach ($arrayTempleBig as $key => $item) {
            $arrayDataTemple = [ //node 1
                'countMiss'   => 0, // số lần bộ xiên 4 không ăn
                'numberBet'   => $item, // bộ xiên 4
                'amount'      => 10, // số tiền
                'status'      => true, // số tiền
            ];

            array_push($arrayResult, $arrayDataTemple);
        }

        return $arrayResult;
    }

    public function cacheProfit()
    {
        return $this->cacheOf('take_profit_15_three');
    }

    public function getStatusProfit()
    {
        return Cache::get($this->cacheProfit(), false);
    }
}
