<?php

/**
 * Demo test cho hàm checkCall4 đã được cập nhật theo logic TruotType15New3ThreeKeyController
 */

/**
 * <PERSON>h tất cả tổ hợp 3 số từ mảng numberDuDoan (giống TruotType15New3ThreeKeyController)
 */
function generateCombinations($numberDuDoan)
{
    $combinations = [];
    $count = count($numberDuDoan);
    
    // Sinh tất cả tổ hợp 3 số theo vị trí
    for ($i = 0; $i < $count - 2; $i++) {
        for ($j = $i + 1; $j < $count - 1; $j++) {
            for ($k = $j + 1; $k < $count; $k++) {
                // Format giống key3.txt: ["07","77","62"]
                $combination = [
                    str_pad($numberDuDoan[$i], 2, '0', STR_PAD_LEFT),
                    str_pad($numberDuDoan[$j], 2, '0', STR_PAD_LEFT),
                    str_pad($numberDuDoan[$k], 2, '0', STR_PAD_LEFT)
                ];
                $combinations[] = $combination;
            }
        }
    }
    
    return $combinations;
}

/**
 * Cập nhật dataBet với các tổ hợp mới từ numberDuDoan (giống TruotType15New3ThreeKeyController)
 */
function updateDataBetWithCombinations($dataBet, $numberDuDoan)
{
    // Sinh tất cả tổ hợp 3 số
    $combinations = generateCombinations($numberDuDoan);
    
    $updatedDataBet = [];
    
    // Cập nhật từng item trong dataBet với tổ hợp mới
    foreach ($combinations as $index => $combination) {
        // Nếu có item cũ thì giữ lại các thuộc tính khác, chỉ thay numberBet
        if (isset($dataBet[$index])) {
            $item = $dataBet[$index];
            $item['numberBet'] = $combination;
        } else {
            // Tạo item mới nếu không có
            $item = [
                'countMiss' => 0,
                'numberBet' => $combination,
                'amount' => 10,
                'status' => true,
            ];
        }
        
        $updatedDataBet[] = $item;
    }
    
    return $updatedDataBet;
}

/**
 * Lấy dữ liệu bet ban đầu (giống TruotType15New3ThreeKeyController)
 */
function getDataBet() 
{
    // Giả lập file key3.txt
    $mockData = [
        ["07","77","62"],
        ["07","77","19"], 
        ["07","77","49"],
        ["07","77","51"],
        ["05","15","25"]
    ];

    $arrayResult = [];
    foreach ($mockData as $key => $item) {
        $arrayDataTemple = [
            'countMiss'   => 0, // số lần bộ xiên 3 không ăn
            'numberBet'   => $item, // bộ xiên 3
            'amount'      => 10, // số tiền
            'status'      => true, // trạng thái
        ];

        array_push($arrayResult, $arrayDataTemple);
    }

    return $arrayResult;
}

/**
 * Demo hàm checkCall4 với logic mới
 */
function demoCheckCall4Updated()
{
    echo "=== DEMO HÀM checkCall4 - LOGIC GIỐNG TruotType15New3ThreeKeyController ===\n\n";
    
    // Giả lập dữ liệu predicted numbers và results cho 10 kỳ
    $demoData = [
        // [predictedNumbers, result]
        [range(1, 60), [7, 77, 62, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60]], // Trúng bộ đầu
        [range(2, 61), [2, 6, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61]],
        [range(3, 62), [3, 7, 11, 14, 17, 20, 23, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62]],
        [range(4, 63), [4, 8, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63]],
        [range(5, 64), [70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89]], // Trượt hoàn toàn
        [range(6, 65), [1, 5, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60]],
        [range(7, 66), [2, 6, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61]],
        [range(8, 67), [90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109]], // Trượt hoàn toàn
        [range(9, 68), [1, 5, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60]],
        [range(10, 69), [2, 6, 10, 13, 16, 19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55, 58, 61]],
    ];
    
    // Khởi tạo biến thống kê chung cho tất cả các bộ số
    $dataResult = array_fill(0, 45, 0);
    
    // Khởi tạo dataBet giống TruotType15New3ThreeKeyController
    $dataBet = null;
    
    // khoi tao cho bet
    if ($dataBet == null) {
        $dataBet = getDataBet();
    }
    
    echo "Khởi tạo với " . count($dataBet) . " bộ số ban đầu:\n";
    foreach ($dataBet as $i => $item) {
        echo "  " . ($i + 1) . ". [\"" . implode('","', $item['numberBet']) . "\"]\n";
    }
    echo "\n";
    
    foreach ($demoData as $ky => $data) {
        $predictedNumbers = $data[0];
        $result = $data[1];
        
        echo "=== KỲ " . ($ky + 21) . " ===\n"; // Bắt đầu từ kỳ 21
        echo "Predicted: [" . implode(',', array_slice($predictedNumbers, 0, 10)) . "...] (60 số)\n";
        echo "Result: [" . implode(',', array_slice($result, 0, 10)) . "...]\n";
        
        // ===== NÂNG CẤP: GEN LẠI TOÀN BỘ SỐ TRONG ARRAY numberBet =====
        if (!empty($predictedNumbers) && count($predictedNumbers) >= 60) {
            $numberDuDoan = array_slice($predictedNumbers, 0, 60);
            $oldCount = count($dataBet);
            $dataBet = updateDataBetWithCombinations($dataBet, $numberDuDoan);
            
            if ($ky == 0) {
                echo "Đã gen lại " . count($dataBet) . " tổ hợp từ 60 số dự đoán (từ $oldCount bộ ban đầu)\n";
                echo "3 bộ đầu tiên sau khi gen:\n";
                for ($i = 0; $i < min(3, count($dataBet)); $i++) {
                    echo "  " . ($i + 1) . ". [\"" . implode('","', $dataBet[$i]['numberBet']) . "\"]\n";
                }
            }
        }
        // ===== KẾT THÚC NÂNG CẤP =====
        
        // array ket qua ky trc (giống TruotType15New3ThreeKeyController)
        $arrayResultPre = array_map(fn($n) => str_pad((string)$n, 2, '0', STR_PAD_LEFT), $result);
        
        $hitCount = 0;
        $missCount = 0;
        $resetCount = 0;
        
        // Kiểm tra kết quả cho từng bộ số trong dataBet
        foreach ($dataBet as $key => $itemDataBet) {
            $arrayBet = $itemDataBet['numberBet']; // Mảng 3 số dạng ["07", "77", "62"]
            $arrayDiffKQ = array_diff($arrayBet, $arrayResultPre);

            if (count($arrayDiffKQ) != 3) { 
                // Có ít nhất 1 số trùng => countMiss + 1
                $itemDataBet['countMiss'] += 1;
                $hitCount++;
            } else { 
                // Cả 3 số đều trượt => thống kê fail streak và reset
                if ($itemDataBet['countMiss'] > 0) {
                    $failStreak = $itemDataBet['countMiss'];
                    if ($failStreak < 45) {
                        $dataResult[$failStreak] += 1; // Thống kê vào biến chung
                    }
                    
                    if ($resetCount < 3) { // Chỉ log 3 bộ đầu để không spam
                        echo "  Bộ [" . implode(',', $arrayBet) . "] trượt hoàn toàn sau $failStreak lần miss\n";
                    }
                    $resetCount++;
                }
                $itemDataBet['countMiss'] = 0; // Reset
                $missCount++;
            }

            $dataBet[$key] = $itemDataBet; // Cập nhật lại
        }
        
        echo "Hit: $hitCount, Miss: $missCount, Reset: $resetCount\n";
        
        $totalMiss = array_sum(array_column($dataBet, 'countMiss'));
        $maxMiss = max(array_column($dataBet, 'countMiss'));
        $avgMiss = $totalMiss / count($dataBet);
        echo "Tổng " . count($dataBet) . " bộ số, Max miss = $maxMiss, Avg miss = " . round($avgMiss, 2) . "\n\n";
    }
    
    // Hiển thị kết quả thống kê
    echo "=== KẾT QUẢ THỐNG KÊ FAIL STREAK ===\n";
    echo "Fail Streak | Số lần xuất hiện\n";
    echo "------------|------------------\n";
    
    $totalEvents = 0;
    for ($i = 1; $i < 45; $i++) {
        if ($dataResult[$i] > 0) {
            echo sprintf("%11d | %16d\n", $i, $dataResult[$i]);
            $totalEvents += $dataResult[$i];
        }
    }
    
    echo "------------|------------------\n";
    echo sprintf("%11s | %16d\n", "TỔNG", $totalEvents);
    
    if ($totalEvents > 0) {
        echo "\n=== PHÂN TÍCH ===\n";
        echo "Tổng số lần trượt hoàn toàn: $totalEvents\n";
        
        $maxCount = max($dataResult);
        $mostCommonStreak = array_search($maxCount, $dataResult);
        echo "Fail streak phổ biến nhất: $mostCommonStreak lần ($maxCount lần xuất hiện)\n";
        
        $totalStreaks = 0;
        for ($i = 1; $i < 45; $i++) {
            $totalStreaks += $i * $dataResult[$i];
        }
        $avgStreak = $totalStreaks / $totalEvents;
        echo "Fail streak trung bình: " . round($avgStreak, 2) . " lần\n";
    }
    
    return $dataResult;
}

echo "=== DEMO LOGIC checkCall4 CẬP NHẬT ===\n\n";

echo "1. THAY ĐỔI CHÍNH:\n";
echo "- Khởi tạo dataBet giống TruotType15New3ThreeKeyController\n";
echo "- Mỗi kỳ gen lại toàn bộ combo từ 60 số dự đoán\n";
echo "- Đếm fail streak cho TẤT CẢ các bộ số vào 1 biến chung\n";
echo "- Logic kiểm tra giống hệt truotXien4\n\n";

echo "2. FLOW:\n";
echo "- Khởi tạo dataBet từ getDataBet()\n";
echo "- Mỗi kỳ: predict -> gen combo -> kiểm tra tất cả bộ\n";
echo "- Thống kê fail streak vào dataResult chung\n\n";

echo "3. DEMO:\n\n";

$result = demoCheckCall4Updated();

echo "\n4. SO SÁNH VỚI LOGIC CŨ:\n";
echo "- CŨ: Chỉ theo dõi combo từ predicted numbers\n";
echo "- MỚI: Theo dõi TẤT CẢ combo được gen từ predicted numbers\n";
echo "- MỚI: Giống hệt logic truotXien4\n";

?>
