# HÀM checkCall4 - THỐNG KÊ FAIL STREAK CÁC BỘ 3 SỐ

## <PERSON><PERSON><PERSON> đích

<PERSON> `checkCall4` được tạo để thống kê fail streak của các bộ 3 số, **GIỐNG HỆT** logic `truotXien4` trong `TruotType15New3ThreeKeyController` nhưng thay vì bet thì là thống kê dữ liệu trong 1 ngày.

## Logic hoạt động (GIỐNG TruotType15New3ThreeKeyController)

### 1. **Khởi tạo dữ liệu**
```php
// Lấy dữ liệu 1 ngày
$service = app()->make(KennoHistoryService::class);
$data = $service->getResultByDay($dayTest);
$history = collect($data)->map(fn($r) => array_map('intval', explode('|', $r['resultRaw'])))->toArray();

// Thống kê từ kỳ 20 đến 119
$a = 20; $b = 119;

// Khởi tạo biến thống kê fail streak từ 0-44 (CHUNG CHO TẤT CẢ BỘ SỐ)
$dataResult = array_fill(0, 45, 0);

// Khởi tạo dataBet giống TruotType15New3ThreeKeyController
$dataBet = null;
if ($dataBet == null) {
    $dataBet = $this->getDataBet(); // Giống hệt TruotType15New3ThreeKeyController
}
```

### 2. **Mỗi kỳ xử lý (GIỐNG HỆT truotXien4)**
```php
for ($ky = $a; $ky <= $b; $ky++) {
    // 1. Lấy kết quả kỳ hiện tại
    $result = $history[$ky];

    // 2. Dự đoán từ AI
    $rounds70 = array_slice($history, 0, $ky);
    $predictedNumbers = callAI($rounds70);

    // 3. ===== NÂNG CẤP: GEN LẠI TOÀN BỘ SỐ TRONG ARRAY numberBet =====
    if (!empty($predictedNumbers) && count($predictedNumbers) >= 60) {
        $numberDuDoan = array_slice($predictedNumbers, 0, 60);
        $dataBet = $this->updateDataBetWithCombinations($dataBet, $numberDuDoan);
    }
    // ===== KẾT THÚC NÂNG CẤP =====

    // 4. array ket qua ky trc (giống TruotType15New3ThreeKeyController)
    $arrayResultPre = array_map(fn($n) => str_pad((string)$n, 2, '0', STR_PAD_LEFT), $result);

    // 5. Kiểm tra kết quả cho từng bộ số trong dataBet (GIỐNG HỆT truotXien4)
    foreach ($dataBet as $key => $itemDataBet) {
        $arrayBet = $itemDataBet['numberBet']; // Mảng 3 số dạng ["07", "77", "62"]
        $arrayDiffKQ = array_diff($arrayBet, $arrayResultPre);

        if (count($arrayDiffKQ) != 3) {
            // Có ít nhất 1 số trùng => countMiss + 1
            $itemDataBet['countMiss'] += 1;
        } else {
            // Cả 3 số đều trượt => thống kê fail streak và reset
            if ($itemDataBet['countMiss'] > 0) {
                $failStreak = $itemDataBet['countMiss'];
                $dataResult[$failStreak] += 1; // THỐNG KÊ VÀO BIẾN CHUNG
            }
            $itemDataBet['countMiss'] = 0; // Reset
        }
        $dataBet[$key] = $itemDataBet; // Cập nhật lại
    }
}
```

### 3. **Điều kiện thống kê (GIỐNG HỆT truotXien4)**
- **Có ít nhất 1 số trùng**: `countMiss++` (giống `$itemDataBet['countMiss'] += 1`)
- **Cả 3 số đều trượt**: Thống kê `countMiss` vào `$dataResult` và reset về 0

### 4. **Điểm khác biệt duy nhất**
- **truotXien4**: Khi đạt ngưỡng thì BET
- **checkCall4**: Khi trượt hoàn toàn thì THỐNG KÊ vào `$dataResult`

## Cách sử dụng

### Command line
```bash
# Chạy thống kê fail streak cho ngày 1
php artisan app:du-doan-10-number --day=1 --port=5049 --mode=checkCall4

# Chạy với ngày khác
php artisan app:du-doan-10-number --day=2 --port=5048 --mode=checkCall4
```

### Trong code
```php
$command = new TestDuDoan10soCommand();
$result = $command->checkCall4($dayTest = 1, $port = 5049);
```

## Kết quả đầu ra

### 1. **Bảng thống kê fail streak**
```
=== KẾT QUẢ THỐNG KÊ FAIL STREAK ===
Fail Streak | Số lần xuất hiện
------------|------------------
          1 |               35
          2 |               85  
          5 |               85
         10 |               12
         15 |                8
         20 |                3
------------|------------------
     TỔNG |              228
```

### 2. **Phân tích chi tiết**
```
=== PHÂN TÍCH CHI TIẾT ===
Tổng số lần trượt hoàn toàn: 228
Fail streak phổ biến nhất: 2 lần (85 lần xuất hiện)
Fail streak trung bình: 3.07 lần

Thống kê theo khoảng:
1-5 lần: 205 (89.9%)
6-10 lần: 15 (6.6%)
11-15 lần: 8 (3.5%)
16-20 lần: 0 (0.0%)
21-25 lần: 0 (0.0%)
26-30 lần: 0 (0.0%)
31+ lần: 0 (0.0%)
```

### 3. **Log chi tiết trong quá trình**
```
Kỳ 25: Khởi tạo 34220 tổ hợp từ 60 số dự đoán
Kỳ 30: Bộ [05,12,23] trượt hoàn toàn sau 8 lần miss
Kỳ 35: Max miss = 15, Avg miss = 5.2
```

## So sánh với truotXien4

| Aspect | truotXien4 | checkCall4 |
|--------|------------|------------|
| **Mục đích** | Bet khi đạt ngưỡng | Thống kê fail streak |
| **Dữ liệu** | Real-time | Lịch sử 1 ngày |
| **Kết quả** | Đặt cược | Bảng thống kê |
| **Logic kiểm tra** | ✅ GIỐNG HỆT | ✅ GIỐNG HỆT |
| **Khởi tạo dataBet** | ✅ GIỐNG HỆT | ✅ GIỐNG HỆT |
| **Gen combo** | ✅ GIỐNG HỆT | ✅ GIỐNG HỆT |
| **Đếm countMiss** | ✅ GIỐNG HỆT | ✅ GIỐNG HỆT |
| **Combo source** | Từ AI predict | Từ AI predict |
| **Khác biệt duy nhất** | Bet khi đạt ngưỡng | Thống kê khi trượt hoàn toàn |

## Ứng dụng

### 1. **Phân tích pattern**
- Tìm fail streak phổ biến nhất
- Xác định ngưỡng bet tối ưu
- Phân tích xu hướng trượt

### 2. **Tối ưu strategy**
- Điều chỉnh `numberKeyBet` dựa trên thống kê
- Xác định `stopRange` phù hợp
- Tính toán risk/reward

### 3. **Backtesting**
- Test strategy với dữ liệu lịch sử
- So sánh hiệu quả các ngưỡng
- Validate logic trước khi bet thật

## Tham số có thể điều chỉnh

```php
$comboSize = 3;      // Kích thước combo (3 hoặc 4)
$sliceCount = 60;    // Số lượng số từ AI predict (60-70)
$a = 20; $b = 119;   // Khoảng kỳ phân tích
```

## Files liên quan

1. `TestDuDoan10soCommand_Upgraded.php` - Command chứa hàm checkCall4
2. `test_checkCall4_demo.php` - Demo với dữ liệu giả lập (logic cũ)
3. `test_checkCall4_updated_demo.php` - Demo với logic mới (giống truotXien4)
4. `README_CHECKCALL4.md` - Tài liệu này

## Lưu ý

- **Memory**: Với 60 số combo 3 có ~34K tổ hợp, cần đủ RAM
- **Time**: Xử lý 100 kỳ có thể mất vài phút
- **Network**: Cần server Python chạy để predict
- **Data**: Cần dữ liệu lịch sử đầy đủ

## Kết luận

Hàm `checkCall4` là công cụ mạnh mẽ để:
- ✅ Thống kê fail streak chi tiết cho TẤT CẢ bộ số
- ✅ Phân tích pattern betting với logic GIỐNG HỆT truotXien4
- ✅ Tối ưu strategy dựa trên dữ liệu thật
- ✅ Backtesting với logic CHÍNH XÁC như production
- ✅ **QUAN TRỌNG**: Logic 100% giống truotXien4, chỉ khác output

### Điểm mạnh của logic mới:
- **Chính xác**: Giống hệt logic production
- **Toàn diện**: Thống kê TẤT CẢ bộ số được gen từ AI
- **Realistic**: Dữ liệu phản ánh đúng thực tế betting
- **Scalable**: Có thể thống kê hàng chục nghìn bộ số
